package cn.jggroup.fakereports.business.controller;

import cn.jggroup.fakereports.business.service.SteelMakingService;
import cn.jggroup.fakereports.core.domain.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 钢铁生产报表控制器
 * 
 * <AUTHOR>
 * @date 2025/8/14 17:19
 */
@Slf4j
@RestController
@RequestMapping("/api/steel/making")
public class SteelMakingController {

    @Autowired
    private SteelMakingService steelMakingService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 根据日期生成所有报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/generate")
    public Response generateReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到生成报表请求，日期: {}", dateFormat.format(date));
        
        long startTime = System.currentTimeMillis();
        int count = steelMakingService.generateReportByDate(date);
        long endTime = System.currentTimeMillis();
        
        log.info("报表生成完成，日期: {}, 数据量: {}, 耗时: {}ms", 
                dateFormat.format(date), count, endTime - startTime);
        
        return Response.SUCCESS;
    }

    /**
     * 根据日期删除报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @DeleteMapping("/delete")
    public Response deleteReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到删除报表请求，日期: {}", dateFormat.format(date));
        
        int count = steelMakingService.deleteReportByDate(date);
        
        log.info("报表删除完成，日期: {}, 删除数据量: {}", dateFormat.format(date), count);
        
        return Response.SUCCESS;
    }

    /**
     * 生成转炉报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/bof")
    public Response generateBofReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到生成转炉报表请求，日期: {}", dateFormat.format(date));
        
        long startTime = System.currentTimeMillis();
        int count = steelMakingService.generateBof(date);
        long endTime = System.currentTimeMillis();
        
        log.info("转炉报表生成完成，日期: {}, 数据量: {}, 耗时: {}ms", 
                dateFormat.format(date), count, endTime - startTime);
        
        return Response.SUCCESS;
    }

    /**
     * 生成精炼报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/lf")
    public Response generateLfReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到生成精炼报表请求，日期: {}", dateFormat.format(date));
        
        long startTime = System.currentTimeMillis();
        int count = steelMakingService.generateLf(date);
        long endTime = System.currentTimeMillis();
        
        log.info("精炼报表生成完成，日期: {}, 数据量: {}, 耗时: {}ms", 
                dateFormat.format(date), count, endTime - startTime);
        
        return Response.SUCCESS;
    }

    /**
     * 生成方坯报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/fp")
    public Response generateFpReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到生成方坯报表请求，日期: {}", dateFormat.format(date));
        
        long startTime = System.currentTimeMillis();
        int count = steelMakingService.generateCcmFp(date);
        long endTime = System.currentTimeMillis();
        
        log.info("方坯报表生成完成，日期: {}, 数据量: {}, 耗时: {}ms", 
                dateFormat.format(date), count, endTime - startTime);
        
        return Response.SUCCESS;
    }

    /**
     * 生成板坯报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/bp")
    public Response generateBpReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到生成板坯报表请求，日期: {}", dateFormat.format(date));
        
        long startTime = System.currentTimeMillis();
        int count = steelMakingService.generateCcmBp(date);
        long endTime = System.currentTimeMillis();
        
        log.info("板坯报表生成完成，日期: {}, 数据量: {}, 耗时: {}ms", 
                dateFormat.format(date), count, endTime - startTime);
        
        return Response.SUCCESS;
    }

    /**
     * 生成生产日报表数据
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/daily")
    public Response generateDailyReport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("接收到生成生产日报请求，日期: {}", dateFormat.format(date));
        
        long startTime = System.currentTimeMillis();
        int count = steelMakingService.generateDailyReporter(date);
        long endTime = System.currentTimeMillis();
        
        log.info("生产日报生成完成，日期: {}, 数据量: {}, 耗时: {}ms", 
                dateFormat.format(date), count, endTime - startTime);
        
        return Response.SUCCESS;
    }

    /**
     * 获取报表生成状态
     * @param date 日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @GetMapping("/status")
    public Response getReportStatus(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        
        log.info("查询报表状态，日期: {}", dateFormat.format(date));
        
        return Response.SUCCESS;
    }

    /**
     * 批量生成报表数据
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 结果
     */
    @PostMapping("/batch")
    public Response batchGenerateReport(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        
        log.info("接收到批量生成报表请求，开始日期: {}, 结束日期: {}", 
                dateFormat.format(startDate), dateFormat.format(endDate));
        
        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        
        long startTime = System.currentTimeMillis();
        int totalCount = 0;
        int dayCount = 0;
        
        Date currentDate = new Date(startDate.getTime());
        while (!currentDate.after(endDate)) {
            int count = steelMakingService.generateReportByDate(currentDate);
            totalCount += count;
            dayCount++;
            
            // 移动到下一天
            currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
        }
        
        long endTime = System.currentTimeMillis();
        
        log.info("批量报表生成完成，日期范围: {} ~ {}, 天数: {}, 总数据量: {}, 耗时: {}ms", 
                dateFormat.format(startDate), dateFormat.format(endDate), 
                dayCount, totalCount, endTime - startTime);
        
        return Response.SUCCESS;
    }
}

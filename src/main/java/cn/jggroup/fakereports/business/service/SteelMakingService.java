package cn.jggroup.fakereports.business.service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/14 17:19
 */
public interface SteelMakingService {

    /**
     * 根据日期生成报表数据
     * @param date 日期
     * @return 影响行数
     */
    int generateReportByDate(Date date);

    /**
     * 根据日期删除报表数据
     * @param date 日期
     * @return 影响行数
     */
    int deleteReportByDate(Date date);

    /**
     * 生成转炉报表
     */
    int generateBof(Date date);
    /**
     * 生成生产指标报表
     */
    int generateDailyReporter(Date date);
    /**
     * 生成方坯报表
     */
    int generateCcmFp(Date date);
    /**
     * 生成板坯报表
     */
    int generateCcmBp(Date date);
    /**
     * 生成精炼报表
     */
    int generateLf(Date date);
}

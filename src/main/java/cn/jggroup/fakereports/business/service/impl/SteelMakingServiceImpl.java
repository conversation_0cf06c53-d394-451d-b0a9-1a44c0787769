package cn.jggroup.fakereports.business.service.impl;

import cn.jggroup.fakereports.business.mapper.CarbonLgmsBofReporterMapper;
import cn.jggroup.fakereports.business.mapper.CarbonLgmsDailyReporterMapper;
import cn.jggroup.fakereports.business.mapper.CarbonLgmsL1CcmFpReporterMapper;
import cn.jggroup.fakereports.business.mapper.CarbonLgmsL2CcmBpReporterMapper;
import cn.jggroup.fakereports.business.mapper.CarbonLgmsL2LfReporterMapper;
import cn.jggroup.fakereports.business.service.SteelMakingService;
import cn.jggroup.fakereports.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 钢铁生产报表服务实现类
 *
 * <AUTHOR>
 * @date 2025/8/14 17:42
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SteelMakingServiceImpl implements SteelMakingService {

    @Autowired
    private CarbonLgmsBofReporterMapper bofReporterMapper;

    @Autowired
    private CarbonLgmsDailyReporterMapper dailyReporterMapper;

    @Autowired
    private CarbonLgmsL1CcmFpReporterMapper l1CcmFpReporterMapper;

    @Autowired
    private CarbonLgmsL2CcmBpReporterMapper l2CcmBpReporterMapper;

    @Autowired
    private CarbonLgmsL2LfReporterMapper l2LfReporterMapper;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public int generateReportByDate(Date date) {
        log.info("开始生成日期 {} 的所有报表数据", dateFormat.format(date));

        // 先删除当日数据，避免重复
        deleteReportByDate(date);

        int totalCount = 0;

        // 生成转炉报表
        int bofCount = generateBof(date);
        totalCount += bofCount;
        log.info("转炉报表生成完成，插入 {} 条数据", bofCount);

        // 生成精炼报表
        int lfCount = generateLf(date);
        totalCount += lfCount;
        log.info("精炼报表生成完成，插入 {} 条数据", lfCount);

        // 生成方坯报表
        int fpCount = generateCcmFp(date);
        totalCount += fpCount;
        log.info("方坯报表生成完成，插入 {} 条数据", fpCount);

        // 生成板坯报表
        int bpCount = generateCcmBp(date);
        totalCount += bpCount;
        log.info("板坯报表生成完成，插入 {} 条数据", bpCount);

        // 生成生产日报
        int dailyCount = generateDailyReporter(date);
        totalCount += dailyCount;
        log.info("生产日报生成完成，插入 {} 条数据", dailyCount);

        log.info("日期 {} 的所有报表数据生成完成，共插入 {} 条数据", dateFormat.format(date), totalCount);
        return totalCount;
    }

    @Override
    public int deleteReportByDate(Date date) {
        log.info("开始删除日期 {} 的报表数据", dateFormat.format(date));

        int totalCount = 0;

        // 删除转炉报表数据
        int bofCount = bofReporterMapper.deleteByDate(date);
        totalCount += bofCount;

        // 删除精炼报表数据
        int lfCount = l2LfReporterMapper.deleteByDate(date);
        totalCount += lfCount;

        // 删除方坯报表数据
        int fpCount = l1CcmFpReporterMapper.deleteByDate(date);
        totalCount += fpCount;

        // 删除板坯报表数据
        int bpCount = l2CcmBpReporterMapper.deleteByDate(date);
        totalCount += bpCount;

        // 删除生产日报数据
        int dailyCount = dailyReporterMapper.deleteByDate(date);
        totalCount += dailyCount;

        log.info("日期 {} 的报表数据删除完成，共删除 {} 条数据", dateFormat.format(date), totalCount);
        return totalCount;
    }

    @Override
    public int generateBof(Date date) {
        log.info("开始生成日期 {} 的转炉报表数据", dateFormat.format(date));

        // 先删除当日转炉数据
        bofReporterMapper.deleteByDate(date);

        // 生成转炉数据
        int count = bofReporterMapper.insertFromSourceByL1(date);
        log.info("L1转炉报表数据生成完成，共插入 {} 条数据", count);
        count = bofReporterMapper.insertFromSourceByL2(date);
        log.info("L2转炉报表数据生成完成，共插入 {} 条数据", count);
        return count;
    }

    @Override
    public int generateDailyReporter(Date date) {
        log.info("开始生成日期 {} 的生产日报数据", dateFormat.format(date));

        // 先删除当日数据
        dailyReporterMapper.deleteByDate(date);

        // 生成生产日报数据
        int count = dailyReporterMapper.insertFromSourceByL1(date);
        log.info("L1生产日报数据生成完成，共插入 {} 条数据", count);
        count = dailyReporterMapper.insertFromSourceByL2(date);
        log.info("L2生产日报数据生成完成，共插入 {} 条数据", count);
        return count;
    }

    @Override
    public int generateCcmFp(Date date) {
        log.info("开始生成日期 {} 的方坯报表数据", dateFormat.format(date));

        // 先删除当日数据
        l1CcmFpReporterMapper.deleteByDate(date);

        // 生成方坯报表数据
        int count = l1CcmFpReporterMapper.insertFromSource(date);

        log.info("方坯报表数据生成完成，共插入 {} 条数据", count);
        return count;
    }

    @Override
    public int generateCcmBp(Date date) {
        log.info("开始生成日期 {} 的板坯报表数据", dateFormat.format(date));

        // 先删除当日数据
        l2CcmBpReporterMapper.deleteByDate(date);

        // 生成板坯报表数据
        int count = l2CcmBpReporterMapper.insertFromSource(date);

        log.info("板坯报表数据生成完成，共插入 {} 条数据", count);
        return count;
    }

    @Override
    public int generateLf(Date date) {
        log.info("开始生成日期 {} 的精炼报表数据", dateFormat.format(date));

        // 先删除当日数据
        l2LfReporterMapper.deleteByDate(date);

        // 生成精炼报表数据
        int count = l2LfReporterMapper.insertFromSource(date);

        log.info("精炼报表数据生成完成，共插入 {} 条数据", count);
        return count;
    }
}

package cn.jggroup.fakereports.business.entity.steel;

import lombok.Data;
import java.util.Date;

/**
 * 碳排放-生产日报实体类
 */
@Data
public class CarbonLgmsDailyReporter {

    /** 报表日期 **/
    private Date reportDate;
    /** 单位代码(L1-一钢,L2-二钢) **/
    private String unitCode;
    /** 单位名称 **/
    private String unitName;
    

    /** 当日炉数 **/
    private Double dailyFurnaceCount;
    /** 当日钢坯产量(吨) **/
    private Double dailySlabOutput;
    /** 当日铁水消耗量(吨) **/
    private Double dailyIronConsumption;
    /** 当日废钢消耗量(吨) **/
    private Double dailyScrapConsumption;
    /** 当日铁水单耗(kg/t) **/
    private Double dailyIronUnitConsumption;
    /** 当日废钢单耗(kg/t) **/
    private Double dailyScrapUnitConsumption;
    /** 当日钢铁料消耗(kg/t) **/
    private Double dailySteelIronConsumption;
    /** 当日白灰单耗(kg/t) **/
    private Double dailyLimeUnitConsumption;
    /** 当日白云石单耗(kg/t) **/
    private Double dailyDolomiteUnitConsumption;
    /** 当日轻烧白云石单耗(kg/t) **/
    private Double dailyLightDolomiteUnitConsumption;
    /** 当日硅锰合金单耗(kg/t) **/
    private Double dailySimnUnitConsumption;
    /** 当日硅铁单耗(kg/t) **/
    private Double dailySifeUnitConsumption;
    /** 当日窑渣单耗(kg/t) **/
    private Double dailyKilnSlagUnitConsumption;
    

    /** 累计炉数 **/
    private Double cumulativeFurnaceCount;
    /** 累计钢坯产量(吨) **/
    private Double cumulativeSlabOutput;
    /** 累计铁水消耗量(吨) **/
    private Double cumulativeIronConsumption;
    /** 累计废钢消耗量(吨) **/
    private Double cumulativeScrapConsumption;
    /** 累计铁水单耗(kg/t) **/
    private Double cumulativeIronUnitConsumption;
    /** 累计废钢单耗(kg/t) **/
    private Double cumulativeScrapUnitConsumption;
    /** 累计钢铁料消耗(kg/t) **/
    private Double cumulativeSteelIronConsumption;
    /** 累计白灰单耗(kg/t) **/
    private Double cumulativeLimeUnitConsumption;
    /** 累计白云石单耗(kg/t) **/
    private Double cumulativeDolomiteUnitConsumption;
    /** 累计轻烧白云石单耗(kg/t) **/
    private Double cumulativeLightDolomiteUnitConsumption;
    /** 累计硅锰合金单耗(kg/t) **/
    private Double cumulativeSimnUnitConsumption;
    /** 累计硅铁单耗(kg/t) **/
    private Double cumulativeSifeUnitConsumption;
    /** 累计窑渣单耗(kg/t) **/
    private Double cumulativeKilnSlagUnitConsumption;
}
package cn.jggroup.fakereports.business.mapper;

import cn.jggroup.fakereports.business.entity.steel.CarbonLgmsDailyReporter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14 17:34
 */
@Mapper
public interface CarbonLgmsDailyReporterMapper {
    /**
     * 插入转炉报表数据
     */
    int insertAll(List<CarbonLgmsDailyReporter> list);

    /**
     * 从源表查询L1数据
     */
    List<CarbonLgmsDailyReporter> selectL1FromSource(Date date);

    /**
     * 从源表查询L2数据
     */
    List<CarbonLgmsDailyReporter> selectL2FromSource(Date date);

    /**
     * 根据日期从源表查询数据并插入到报表表
     */
    int insertFromSource(@Param("date") Date date);

    /**
     * 根据日期从源表查询L1数据并插入到报表表
     */
    int insertFromSourceByL1(@Param("date") Date date);

    /**
     * 根据日期从源表查询L2数据并插入到报表表
     */
    int insertFromSourceByL2(@Param("date") Date date);

    /**
     * 根据日期删除数据
     */
    int deleteByDate(@Param("date") Date date);

    /**
     * 清空表数据
     */
    int truncate();
}

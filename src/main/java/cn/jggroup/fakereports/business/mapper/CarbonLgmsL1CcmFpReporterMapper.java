package cn.jggroup.fakereports.business.mapper;

import cn.jggroup.fakereports.business.entity.steel.CarbonLgmsL1CcmFpReporter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14 17:34
 */
@Mapper
public interface CarbonLgmsL1CcmFpReporterMapper {
    /**
     * 插入转炉报表数据
     */
    int insertAll(List<CarbonLgmsL1CcmFpReporter> list);

    /**
     * 从源表查询L1数据
     */
    List<CarbonLgmsL1CcmFpReporter> selectFromSource(Date date);


    /**
     * 根据日期从源表查询数据并插入到报表表
     */
    int insertFromSource(@Param("date") Date date);

    /**
     * 根据日期删除数据
     */
    int deleteByDate(@Param("date") Date date);

    /**
     * 清空表数据
     */
    int truncate();
}

package cn.jggroup.fakereports.core;

import cn.jggroup.fakereports.core.domain.Response;
import cn.jggroup.fakereports.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 * @date 2025/8/16 15:00
 */
@Slf4j
@RestControllerAdvice
public class MainControllerAdvice {

    /**
     * 处理业务异常
     * @param e 业务异常
     * @return 响应结果
     */
    @ExceptionHandler(BusinessException.class)
    public Response handleBusinessException(BusinessException e) {
        log.error("业务异常: {}", e.getMessage(), e);
        return Response.error(e.getMessage());
    }

    /**
     * 处理参数异常
     * @param e 参数异常
     * @return 响应结果
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Response handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("参数异常: {}", e.getMessage(), e);
        return Response.error("参数错误: " + e.getMessage());
    }

    /**
     * 处理运行时异常
     * @param e 运行时异常
     * @return 响应结果
     */
    @ExceptionHandler(RuntimeException.class)
    public Response handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        return Response.error("系统异常，请联系管理员");
    }

    /**
     * 处理其他异常
     * @param e 异常
     * @return 响应结果
     */
    @ExceptionHandler(Throwable.class)
    public Response handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        return Response.error("系统异常，请联系管理员");
    }
}
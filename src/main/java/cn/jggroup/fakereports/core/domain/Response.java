package cn.jggroup.fakereports.core.domain;

import cn.jggroup.fakereports.core.enums.ResultCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "通用返回对象（不带泛型）")
public class Response {
    /**
     * 默认业务状态请求失败
     **/
    @Schema(description = "状态码")
    private String code;
    /**
     * 返回信息
     **/
    @Schema(description = "返回信息")
    private String message;
    /**
     * 返回数据
     **/
    @Schema(description = "返回数据")
    private Object data;

    public Response(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通用成功
     **/
    public static final Response SUCCESS = new Response(ResultCodeEnum.SUCCESS.getCode(),
            ResultCodeEnum.SUCCESS.getContext());
    /**
     * 通用失败
     **/
    public static final Response ERROR = new Response(ResultCodeEnum.ERROR.getCode(),
            ResultCodeEnum.ERROR.getContext());
    /**
     * 通用服务器异常
     **/
    public static final Response SERVER_ERROR = new Response(ResultCodeEnum.SERVER_ERROR.getCode(),
            ResultCodeEnum.SERVER_ERROR.getContext());
    public static final Response PARAM_ERROR = new Response(ResultCodeEnum.REQUEST_METHOD_NOT_SUPPORT.getCode(),
            ResultCodeEnum.REQUEST_METHOD_NOT_SUPPORT.getContext());
    public static final Response UNAUTHORIZED = new Response(ResultCodeEnum.UNAUTHORIZED.getCode(),
            ResultCodeEnum.UNAUTHORIZED.getContext());
    public static final Response FORBIDDEN = new Response(ResultCodeEnum.FORBIDDEN.getCode(),
            ResultCodeEnum.FORBIDDEN.getContext());
    public static final Response REQUEST_METHOD_NOT_SUPPORT =
            new Response(ResultCodeEnum.REQUEST_METHOD_NOT_SUPPORT.getCode(),
                    ResultCodeEnum.REQUEST_METHOD_NOT_SUPPORT.getContext());
    public static final Response NOT_ACCEPTABLE = new Response(ResultCodeEnum.NOT_ACCEPTABLE.getCode(),
            ResultCodeEnum.NOT_ACCEPTABLE.getContext());
    public static final Response NOT_FOUND = new Response(ResultCodeEnum.NOT_FOUND.getCode(),
            ResultCodeEnum.NOT_FOUND.getContext());
    public static final Response SERVER_UNAUTHORIZED = new Response(ResultCodeEnum.SERVER_UNAUTHORIZED.getCode(),
            ResultCodeEnum.SERVER_UNAUTHORIZED.getContext());


    /**
     * 通用成功，需要返回数据时使用
     */
    public static Response ok(Object data) {
        return new Response(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getContext(), data);
    }

    /**
     * 通用失败，需要返回数据时使用
     */
    public static Response internalServerError(Object data) {
        return new Response(ResultCodeEnum.ERROR.getCode(), ResultCodeEnum.ERROR.getContext(), data);
    }

    /**
     * 通用失败，需要自定义消息时使用，一般推荐使用自定义状态码
     */
    public static Response internalServerError(String errorMsg) {
        return new Response(ResultCodeEnum.ERROR.getCode(), errorMsg);
    }

    /**
     * 通用失败，需要自定义消息时使用
     */
    public static Response error(String errorMsg) {
        return new Response(ResultCodeEnum.ERROR.getCode(), errorMsg);
    }

    /**
     * 通用失败，需要返回数据时使用
     */
    public static Response error(String errorMsg, Object data) {
        return new Response(ResultCodeEnum.ERROR.getCode(), errorMsg, data);
    }

    /**
     * 判断是否成功
     */
    public boolean checkSuccess() {
        if (Objects.equals(this.getCode(), ResultCodeEnum.SUCCESS.getCode())) {
            return true;
        }
        return false;
    }
}

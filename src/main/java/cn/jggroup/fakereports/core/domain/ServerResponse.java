package cn.jggroup.fakereports.core.domain;

import cn.jggroup.fakereports.core.enums.ResultCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "通用返回对象（带泛型）")
public class ServerResponse<T> implements Serializable {
    /** 返回代码 **/
    @Schema(description = "状态码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;
    /** 返回处理信息 **/
    @Schema(description = "处理信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private String message;
    /** 返回数据对象 **/
    @Schema(description = "数据对象")
    private T data;

    public ServerResponse(String code, String msg) {
        this(code, msg, null);
    }

    public ServerResponse(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static final String SUCCESS_CODE = "200";
    public static final String SUCCESS_MSG = "成功";
    public static final String ERROR_CODE = "-1";
    public static final String UNAUTHORIZED_ERROR_CODE = "402";
    public static final String ERROR_MSG = "失败";

    /**
     * 通用成功
     **/
    public static final ServerResponse SUCCESS = new ServerResponse(ResultCodeEnum.SUCCESS.getCode(),
            ResultCodeEnum.SUCCESS.getContext());
    /**
     * 通用失败
     **/
    public static final ServerResponse ERROR = new ServerResponse(ResultCodeEnum.ERROR.getCode(),
            ResultCodeEnum.ERROR.getContext());

    public static <T> ServerResponse<T> ok(T data) {
        return new ServerResponse<>(SUCCESS_CODE, SUCCESS_MSG, data);
    }

    public static <T> ServerResponse<T> ok(String message, T data) {
        return new ServerResponse<>(SUCCESS_CODE, message, data);
    }

    public static <T> ServerResponse<T> internalServerError(String msg) {
        return new ServerResponse<>(ERROR_CODE, msg);
    }

    public static <T> ServerResponse<T> internalServerError(String msg, T data) {
        return new ServerResponse<T>(ERROR_CODE, msg, data);
    }

    public static <T> ServerResponse<T> withCode(String code, String msg, T data) {
        return new ServerResponse<T>(code, msg, data);
    }
}

package cn.jggroup.fakereports.core.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:04
 */
@Getter
@ToString
public enum ResultCodeEnum {
    //--------------------通用处理-------------------
    SUCCESS("200", "成功"),
    ERROR("-1", "失败"),
    SERVER_ERROR("500", "网络异常"),
    REQUEST_METHOD_NOT_SUPPORT("400", "不支持的请求方式"),
    UNAUTHORIZED("401", "未经授权的访问"),
    SERVER_UNAUTHORIZED("402", "系统未授权或者授权已过期"),
    FORBIDDEN("403", "您没有访问该资源的权限"),
    NOT_FOUND("404", "无法访问资源"),
    NOT_ACCEPTABLE("406", "请求消息体错误");

    private final String code;
    private final String context;

    private ResultCodeEnum(String code, String context) {
        this.code = code;
        this.context = context;
    }
}

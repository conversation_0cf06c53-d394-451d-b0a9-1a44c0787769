package cn.jggroup.fakereports.core.enums;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONString;
import com.baomidou.mybatisplus.annotation.IEnum;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
public interface BaseEnum extends IEnum<Integer>, JSONString {
    /**
     * 获取枚举编码
     */
    Integer getCode();

    /**
     * 获取枚举描述
     */
    String getDescribe();

    @Override
    default Integer getValue() {
        return getCode();
    }

    @Override
    default String toJSONString() {
        Class<? extends BaseEnum> clazz = getClass();
        Method name = ReflectUtil.getMethodByName(clazz, "name");
        Object nameValue = ReflectUtil.invoke(this, name);
        return nameValue.toString();
    }
}

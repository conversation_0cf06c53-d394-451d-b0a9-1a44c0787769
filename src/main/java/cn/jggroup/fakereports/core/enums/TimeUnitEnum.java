package cn.jggroup.fakereports.core.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:00
 */
@ToString
@Getter
public enum TimeUnitEnum implements BaseEnum {
    DAY(1, "今日"),
    WEEK(2, "周"),
    MONTH(3, "月"),
    QUARTER(4, "季"),
    HALF_YEAR(5, "半年"),
    YEAR(6, "年"),
    CUSTOM(7, "自定义");
    private final Integer code;
    private final String describe;

    TimeUnitEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

}


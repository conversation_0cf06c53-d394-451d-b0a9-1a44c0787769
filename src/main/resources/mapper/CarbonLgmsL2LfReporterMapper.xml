<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jggroup.fakereports.business.mapper.CarbonLgmsL2LfReporterMapper">

    <!-- 批量插入精炼报表数据 -->
    <insert id="insertAll" parameterType="java.util.List">
        INSERT INTO JGLGMES.CARBON_LGMS_L2_LF_REPORTER (
            HEAT_ID, WORK_SHOP, P_DATE, SHIFT, GROUP1, MAC_CODE,
            LF_NO, LF_START, LF_END, LF_TIME, LF_POWER_TIME,
            TARGET_TMP, START_TMP, END_TMP, POWER_ON_TMP, POWER_OFF_TMP,
            AR_FLOW, AR_TIME, AR_FLOW_TOTAL,
            LIME_STONE, D<PERSON><PERSON>IT<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ALUMINUM_SHOT, SILICON_CARBIDE,
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SILICON_MANGANESE, ALUMINUM_WIRE, CALCIUM_WIRE,
            CARBON_POWDER, SILICON_ALUMINUM_WIRE, SILICON_ALUMINUM_CALCIUM_WIRE,
            MAGNESIUM_ALUMINUM_WIRE, TITANIUM_IRON_WIRE, BORON_IRON_WIRE,
            RARE_EARTH_WIRE, SULFUR_WIRE, CARBON_WIRE, NITROGEN_WIRE,
            LF_C, LF_MN, LF_SI, LF_S, LF_P, LF_V, LF_AL, LF_N, LF_CA, LF_TI,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA, CCM_TI,
            REMARK
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.heatId}, #{item.workShop}, #{item.pDate}, #{item.shift}, #{item.group1}, #{item.macCode},
                #{item.lfNo}, #{item.lfStart}, #{item.lfEnd}, #{item.lfTime}, #{item.lfPowerTime},
                #{item.targetTmp}, #{item.startTmp}, #{item.endTmp}, #{item.powerOnTmp}, #{item.powerOffTmp},
                #{item.arFlow}, #{item.arTime}, #{item.arFlowTotal},
                #{item.limeStone}, #{item.dolomite}, #{item.fluorspar}, #{item.aluminumShot}, #{item.siliconCarbide},
                #{item.ferrosilicon}, #{item.siliconManganese}, #{item.aluminumWire}, #{item.calciumWire},
                #{item.carbonPowder}, #{item.siliconAluminumWire}, #{item.siliconAluminumCalciumWire},
                #{item.magnesiumAluminumWire}, #{item.titaniumIronWire}, #{item.boronIronWire},
                #{item.rareEarthWire}, #{item.sulfurWire}, #{item.carbonWire}, #{item.nitrogenWire},
                #{item.lfC}, #{item.lfMn}, #{item.lfSi}, #{item.lfS}, #{item.lfP}, #{item.lfV}, #{item.lfAl}, #{item.lfN}, #{item.lfCa}, #{item.lfTi},
                #{item.ccmC}, #{item.ccmMn}, #{item.ccmSi}, #{item.ccmS}, #{item.ccmP}, #{item.ccmV}, #{item.ccmAl}, #{item.ccmN}, #{item.ccmCa}, #{item.ccmTi},
                #{item.remark}
            )
        </foreach>
    </insert>

    <!-- 从源表查询数据 -->
    <select id="selectFromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsL2LfReporter">
        SELECT
            A.HEAT_ID as heatId,
            'L2' as workShop,
            A.LF_DATE as pDate,
            A.SHIFT as shift,
            A.GROUP1 as group1,
            A.MAC_CODE as macCode,
            A.LF_NO as lfNo,
            A.LF_START as lfStart,
            A.LF_END as lfEnd,
            ROUND(TO_NUMBER(A.LF_END - A.LF_START) * 24 * 60) as lfTime,
            A.LF_POWER_TIME as lfPowerTime,
            A.TARGET_TMP as targetTmp,
            A.START_TMP as startTmp,
            A.END_TMP as endTmp,
            A.POWER_ON_TMP as powerOnTmp,
            A.POWER_OFF_TMP as powerOffTmp,
            A.AR_FLOW as arFlow,
            A.AR_TIME as arTime,
            A.AR_FLOW_TOTAL as arFlowTotal,
            G.石灰石 as limeStone,
            G.白云石 as dolomite,
            G.萤石 as fluorspar,
            G.铝粒 as aluminumShot,
            G.碳化硅 as siliconCarbide,
            G.硅铁 as ferrosilicon,
            G.硅锰 as siliconManganese,
            G.铝线 as aluminumWire,
            G.钙线 as calciumWire,
            G.碳粉 as carbonPowder,
            G.硅铝线 as siliconAluminumWire,
            G.硅铝钙线 as siliconAluminumCalciumWire,
            G.镁铝线 as magnesiumAluminumWire,
            G.钛铁线 as titaniumIronWire,
            G.硼铁线 as boronIronWire,
            G.稀土线 as rareEarthWire,
            G.硫线 as sulfurWire,
            G.碳线 as carbonWire,
            G.氮线 as nitrogenWire,
            B.C as lfC,
            B.Mn as lfMn,
            B.Si as lfSi,
            B.S as lfS,
            B.P as lfP,
            B.V as lfV,
            B.AL as lfAl,
            B.N as lfN,
            B.CA as lfCa,
            B.TI as lfTi,
            C.C as ccmC,
            C.Mn as ccmMn,
            C.Si as ccmSi,
            C.S as ccmS,
            C.P as ccmP,
            C.V as ccmV,
            C.AL as ccmAl,
            C.N as ccmN,
            C.CA as ccmCa,
            C.TI as ccmTi,
            A.REMARK as remark
        FROM JGLGMES.SMES_B_LF_RES A
        LEFT JOIN (
            SELECT *
            FROM JGLGMES.SMES_B_che_steel a
            WHERE a.SEQ_NO IN (
                SELECT MAX(b.SEQ_NO)
                FROM JGLGMES.SMES_B_che_steel b
                WHERE a.HEAT_ID = b.HEAT_ID
                GROUP BY SUBSTR(SAMPLETYPE, 0, 1)
            )
            AND sampletype LIKE 'L%'
        ) B ON A.HEAT_ID = B.HEAT_ID
        LEFT JOIN (
            SELECT *
            FROM JGLGMES.SMES_B_che_steel a
            WHERE a.SEQ_NO IN (
                SELECT MAX(b.SEQ_NO)
                FROM JGLGMES.SMES_B_che_steel b
                WHERE a.HEAT_ID = b.HEAT_ID
                GROUP BY SUBSTR(SAMPLETYPE, 0, 1)
            )
            AND sampletype LIKE 'C%'
        ) C ON A.HEAT_ID = C.HEAT_ID
        LEFT JOIN (
            SELECT T.HEAT_ID,
                   SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT, '10501012', T.MAT_WGT, 0)) 石灰石,
                   SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT, '10501029', T.MAT_WGT, 0)) 白云石,
                   SUM(DECODE(T.MAT_CODE, '10501018', T.MAT_WGT, 0)) 萤石,
                   SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 铝粒,
                   SUM(DECODE(T.MAT_CODE, '10501019', T.MAT_WGT, 0)) 碳化硅,
                   SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                   SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰,
                   SUM(DECODE(T.MAT_CODE, '10202001', T.MAT_WGT, 0)) 铝线,
                   SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 钙线,
                   SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 碳粉,
                   SUM(DECODE(T.MAT_CODE, '10202002', T.MAT_WGT, 0)) 硅铝线,
                   SUM(DECODE(T.MAT_CODE, '10202003', T.MAT_WGT, 0)) 硅铝钙线,
                   SUM(DECODE(T.MAT_CODE, '10202004', T.MAT_WGT, 0)) 镁铝线,
                   SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, 0)) 钛铁线,
                   SUM(DECODE(T.MAT_CODE, '10202012', T.MAT_WGT, 0)) 硼铁线,
                   SUM(DECODE(T.MAT_CODE, '10202013', T.MAT_WGT, 0)) 稀土线,
                   SUM(DECODE(T.MAT_CODE, '10202014', T.MAT_WGT, 0)) 硫线,
                   SUM(DECODE(T.MAT_CODE, '10202015', T.MAT_WGT, 0)) 碳线,
                   SUM(DECODE(T.MAT_CODE, '10202016', T.MAT_WGT, 0)) 氮线
            FROM JGLGMES.SMES_B_MAT_USE T
            WHERE T.wp_code = '2C01'
            GROUP BY T.HEAT_ID
        ) G ON A.HEAT_ID = G.HEAT_ID
        WHERE A.WORK_SHOP = 'L2'
          AND TO_CHAR(A.LF_DATE, 'YYYY-MM-DD') = TO_CHAR(#{date}, 'YYYY-MM-DD')
        ORDER BY A.LF_START ASC
    </select>

    <!-- 根据日期从源表查询数据并插入到报表表 -->
    <insert id="insertFromSource" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_L2_LF_REPORTER (
            HEAT_ID, WORK_SHOP, P_DATE, SHIFT, GROUP1, MAC_CODE,
            LF_NO, LF_START, LF_END, LF_TIME, LF_POWER_TIME,
            TARGET_TMP, START_TMP, END_TMP, POWER_ON_TMP, POWER_OFF_TMP,
            AR_FLOW, AR_TIME, AR_FLOW_TOTAL,
            LIME_STONE, DOLOMITE, FLUORSPAR, ALUMINUM_SHOT, SILICON_CARBIDE,
            FERROSILICON, SILICON_MANGANESE, ALUMINUM_WIRE, CALCIUM_WIRE,
            CARBON_POWDER, SILICON_ALUMINUM_WIRE, SILICON_ALUMINUM_CALCIUM_WIRE,
            MAGNESIUM_ALUMINUM_WIRE, TITANIUM_IRON_WIRE, BORON_IRON_WIRE,
            RARE_EARTH_WIRE, SULFUR_WIRE, CARBON_WIRE, NITROGEN_WIRE,
            LF_C, LF_MN, LF_SI, LF_S, LF_P, LF_V, LF_AL, LF_N, LF_CA, LF_TI,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA, CCM_TI,
            REMARK
        )
        SELECT
            A.HEAT_ID,
            'L2',
            A.LF_DATE,
            A.SHIFT,
            A.GROUP1,
            A.MAC_CODE,
            A.LF_NO,
            A.LF_START,
            A.LF_END,
            ROUND(TO_NUMBER(A.LF_END - A.LF_START) * 24 * 60),
            A.LF_POWER_TIME,
            A.TARGET_TMP,
            A.START_TMP,
            A.END_TMP,
            A.POWER_ON_TMP,
            A.POWER_OFF_TMP,
            A.AR_FLOW,
            A.AR_TIME,
            A.AR_FLOW_TOTAL,
            G.石灰石,
            G.白云石,
            G.萤石,
            G.铝粒,
            G.碳化硅,
            G.硅铁,
            G.硅锰,
            G.铝线,
            G.钙线,
            G.碳粉,
            G.硅铝线,
            G.硅铝钙线,
            G.镁铝线,
            G.钛铁线,
            G.硼铁线,
            G.稀土线,
            G.硫线,
            G.碳线,
            G.氮线,
            B.C,
            B.Mn,
            B.Si,
            B.S,
            B.P,
            B.V,
            B.AL,
            B.N,
            B.CA,
            B.TI,
            C.C,
            C.Mn,
            C.Si,
            C.S,
            C.P,
            C.V,
            C.AL,
            C.N,
            C.CA,
            C.TI,
            A.REMARK
        FROM JGLGMES.SMES_B_LF_RES A,
             (SELECT *
              FROM JGLGMES.SMES_B_che_steel a
              WHERE a.SEQ_NO IN (SELECT MAX(b.SEQ_NO)
                                 FROM JGLGMES.SMES_B_che_steel b
                                 WHERE a.HEAT_ID = b.HEAT_ID
                                 GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
                AND sampletype LIKE 'L%') B,
             (SELECT *
              FROM JGLGMES.SMES_B_che_steel a
              WHERE a.SEQ_NO IN (SELECT MAX(b.SEQ_NO)
                                 FROM JGLGMES.SMES_B_che_steel b
                                 WHERE a.HEAT_ID = b.HEAT_ID
                                 GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
                AND sampletype LIKE 'C%') C,
             (SELECT T.HEAT_ID,
                     SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT, '10501012', T.MAT_WGT, 0)) 石灰石,
                     SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT, '10501029', T.MAT_WGT, 0)) 白云石,
                     SUM(DECODE(T.MAT_CODE, '10501018', T.MAT_WGT, 0)) 萤石,
                     SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 铝粒,
                     SUM(DECODE(T.MAT_CODE, '10501019', T.MAT_WGT, 0)) 碳化硅,
                     SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                     SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰,
                     SUM(DECODE(T.MAT_CODE, '10202001', T.MAT_WGT, 0)) 铝线,
                     SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 钙线,
                     SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 碳粉,
                     SUM(DECODE(T.MAT_CODE, '10202002', T.MAT_WGT, 0)) 硅铝线,
                     SUM(DECODE(T.MAT_CODE, '10202003', T.MAT_WGT, 0)) 硅铝钙线,
                     SUM(DECODE(T.MAT_CODE, '10202004', T.MAT_WGT, 0)) 镁铝线,
                     SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, 0)) 钛铁线,
                     SUM(DECODE(T.MAT_CODE, '10202012', T.MAT_WGT, 0)) 硼铁线,
                     SUM(DECODE(T.MAT_CODE, '10202013', T.MAT_WGT, 0)) 稀土线,
                     SUM(DECODE(T.MAT_CODE, '10202014', T.MAT_WGT, 0)) 硫线,
                     SUM(DECODE(T.MAT_CODE, '10202015', T.MAT_WGT, 0)) 碳线,
                     SUM(DECODE(T.MAT_CODE, '10202016', T.MAT_WGT, 0)) 氮线
              FROM JGLGMES.SMES_B_MAT_USE T
              WHERE T.wp_code = '2C01'
              GROUP BY T.HEAT_ID) G
        WHERE A.HEAT_ID = B.HEAT_ID(+)
          AND A.HEAT_ID = C.HEAT_ID(+)
          AND A.HEAT_ID = G.HEAT_ID(+)
          AND A.WORK_SHOP = 'L2'
          AND TO_CHAR(A.LF_DATE, 'YYYY-MM-DD') = TO_CHAR(#{date}, 'YYYY-MM-DD')
        ORDER BY A.LF_START ASC
    </insert>

    <!-- 根据日期删除数据 -->
    <delete id="deleteByDate" parameterType="java.util.Date">
        DELETE FROM JGLGMES.CARBON_LGMS_L2_LF_REPORTER
        WHERE TRUNC(P_DATE) = TRUNC(#{date})
    </delete>

    <!-- 清空表数据 -->
    <delete id="truncate">
        TRUNCATE TABLE JGLGMES.CARBON_LGMS_L2_LF_REPORTER
    </delete>

</mapper>

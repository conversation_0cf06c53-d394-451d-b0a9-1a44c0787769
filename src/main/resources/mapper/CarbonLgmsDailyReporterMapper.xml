<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jggroup.fakereports.business.mapper.CarbonLgmsDailyReporterMapper">

    <!-- 批量插入日报数据 -->
    <insert id="insertAll" parameterType="java.util.List">
        INSERT INTO JGLGMES.CARBON_LGMS_DAILY_REPORTER (
            REPORT_DATE, UNIT_CODE, UNIT_NAME,
            DAILY_FURNACE_COUNT, DAILY_SLAB_OUTPUT, DAILY_IRON_CONSUMPTION, DAILY_SCRAP_CONSUMPTION,
            DAILY_IRON_UNIT_CONSUMPTION, DAILY_SCRAP_UNIT_CONSUMPTION, DAILY_STEEL_IRON_CONSUMPTION,
            DAILY_LIME_UNIT_CONSUMPTION, DAILY_DOLOMITE_UNIT_CONSUMPTION, DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            DAILY_SIMN_UNIT_CONSUMPTION, DAILY_SIFE_UNIT_CONSUMPTION, DAILY_KILN_SLAG_UNIT_CONSUMPTION,
            CUMULATIVE_FURNACE_COUNT, CUMULATIVE_SLAB_OUTPUT, CUMULATIVE_IRON_CONSUMPTION, CUMULATIVE_SCRAP_CONSUMPTION,
            CUMULATIVE_IRON_UNIT_CONSUMPTION, CUMULATIVE_SCRAP_UNIT_CONSUMPTION, CUMULATIVE_STEEL_IRON_CONSUMPTION,
            CUMULATIVE_LIME_UNIT_CONSUMPTION, CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION, CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            CUMULATIVE_SIMN_UNIT_CONSUMPTION, CUMULATIVE_SIFE_UNIT_CONSUMPTION, CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.reportDate}, #{item.unitCode}, #{item.unitName},
                #{item.dailyFurnaceCount}, #{item.dailySlabOutput}, #{item.dailyIronConsumption}, #{item.dailyScrapConsumption},
                #{item.dailyIronUnitConsumption}, #{item.dailyScrapUnitConsumption}, #{item.dailySteelIronConsumption},
                #{item.dailyLimeUnitConsumption}, #{item.dailyDolomiteUnitConsumption}, #{item.dailyLightDolomiteUnitConsumption},
                #{item.dailySimnUnitConsumption}, #{item.dailySifeUnitConsumption}, #{item.dailyKilnSlagUnitConsumption},
                #{item.cumulativeFurnaceCount}, #{item.cumulativeSlabOutput}, #{item.cumulativeIronConsumption}, #{item.cumulativeScrapConsumption},
                #{item.cumulativeIronUnitConsumption}, #{item.cumulativeScrapUnitConsumption}, #{item.cumulativeSteelIronConsumption},
                #{item.cumulativeLimeUnitConsumption}, #{item.cumulativeDolomiteUnitConsumption}, #{item.cumulativeLightDolomiteUnitConsumption},
                #{item.cumulativeSimnUnitConsumption}, #{item.cumulativeSifeUnitConsumption}, #{item.cumulativeKilnSlagUnitConsumption}
            )
        </foreach>
    </insert>

    <!-- 从源表查询L1数据 -->
    <select id="selectL1FromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsDailyReporter">
        WITH
        -- 1. 一钢产量（日）
        daily_prod_l1_cte AS (
            SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_D
            FROM JGLGMES.SMES_B_BOF_RES A
            LEFT JOIN (
                SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
                FROM JGLGMES.smes_b_cutres
                GROUP BY SUBSTR(HEAT_ID, 0, 9)
            ) F ON A.HEAT_ID = F.HEAT_ID
            LEFT JOIN (
                SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
                FROM JGLGMES.SMES_B_CCMRES
                WHERE MAC_CODE IN ('1D311', '1D313', '1D314')
                GROUP BY HEAT_ID
            ) H ON A.HEAT_ID = H.HEAT_ID
            WHERE A.WORK_SHOP = 'L1'
              AND A.MAC_CODE != '1C015'
              AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
        ),
        -- 2. 一钢产量（月累计）
        monthly_prod_l1_cte AS (
            SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_M
            FROM JGLGMES.SMES_B_BOF_RES A
            LEFT JOIN (
                SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
                FROM JGLGMES.smes_b_cutres
                GROUP BY SUBSTR(HEAT_ID, 0, 9)
            ) F ON A.HEAT_ID = F.HEAT_ID
            LEFT JOIN (
                SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
                FROM JGLGMES.SMES_B_CCMRES
                WHERE MAC_CODE IN ('1D311', '1D313', '1D314')
                GROUP BY HEAT_ID
            ) H ON A.HEAT_ID = H.HEAT_ID
            WHERE A.WORK_SHOP = 'L1'
              AND A.MAC_CODE != '1C015'
              AND H.STAR_CAST_TIM BETWEEN TRUNC(#{date}, 'MM') AND #{date} + 1
        ),
        -- 3. 一钢日消耗数据
        daily_consumption_l1_cte AS (
            SELECT 
                NVL(SUM(CC.IRON_WGT), 0) AS IRON_WGT_D,
                NVL(SUM(DD.FG), 0) AS TOTAL_SCRAP_D,
                NVL(SUM(DD.BAIHUI), 0) AS BAIHUI_D,
                NVL(SUM(DD.QSBYS), 0) AS QSBYS_D,
                NVL(SUM(DD.BYS), 0) AS BYS_D,
                NVL(SUM(DD.SHS), 0) AS SHS_D,
                NVL(SUM(DD.GMHJ), 0) AS GMHJ_D,
                NVL(SUM(DD.GUITIE), 0) AS GUITIE_D,
                NVL(SUM(DD.YZ), 0) AS YZ_D
            FROM JGLGMES.SMES_B_CCMRES AA
            LEFT JOIN JGLGMES.SMES_B_BOF_RES CC ON AA.HEAT_ID = CC.HEAT_ID
            LEFT JOIN (
                SELECT HEAT_ID,
                       SUM(DECODE(MAT_CODE, '10401014', MAT_WGT / 1000, '10401015', MAT_WGT / 1000, '10704001', MAT_WGT / 1000, 0)) AS FG,
                       SUM(DECODE(MAT_CODE, '10201001', MAT_WGT, 0)) AS GUITIE,
                       SUM(DECODE(MAT_CODE, '10201006', MAT_WGT, 0)) AS GMHJ,
                       SUM(DECODE(MAT_CODE, '10501024', MAT_WGT, '10705001', MAT_WGT, 0)) AS BAIHUI,
                       SUM(DECODE(MAT_CODE, '10501002', MAT_WGT, '10705003', MAT_WGT, 0)) AS QSBYS,
                       SUM(DECODE(MAT_CODE, '10501015', MAT_WGT, '10501029', MAT_WGT, 0)) AS BYS,
                       SUM(DECODE(MAT_CODE, '10501035', MAT_WGT, '10501036', MAT_WGT, '10501012', MAT_WGT, 0)) AS SHS,
                       SUM(DECODE(MAT_CODE, '10701002', MAT_WGT, 0)) AS YZ
                FROM JGLGMES.SMES_B_MAT_USE
                GROUP BY HEAT_ID
            ) DD ON AA.HEAT_ID = DD.HEAT_ID
            WHERE AA.WORK_SHOP = 'L1'
              AND TRUNC(AA.STAR_CAST_TIM, 'DD') = TRUNC(#{date}, 'DD')
        ),
        -- 4. 一钢炉数（日）
        daily_furnace_l1_cte AS (
            SELECT COUNT(*) AS FURNACE_COUNT_D
            FROM JGLGMES.SMES_B_BOF_RES bof
            WHERE bof.WORK_SHOP = 'L1'
              AND bof.MAC_CODE IN ('1C013', '1C014')
              AND EXISTS (
                  SELECT 1
                  FROM JGLGMES.SMES_B_CCMRES ccm
                  WHERE ccm.heat_id = bof.HEAT_ID
                    AND ccm.WORK_SHOP = 'L1'
                    AND TRUNC(ccm.star_cast_tim, 'DD') = TRUNC(#{date}, 'DD')
                    AND ccm.MAC_CODE in ('1D311','1D313','1D314')
              )
        )
        -- 最终一钢数据查询
        SELECT 
            #{date} as reportDate,
            'L1' as unitCode,
            '炼钢一厂' as unitName,
            d_furnace.FURNACE_COUNT_D as dailyFurnaceCount,
            d_prod.SLAB_WGT_D as dailySlabOutput,
            d_con.IRON_WGT_D as dailyIronConsumption,
            d_con.TOTAL_SCRAP_D as dailyScrapConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.IRON_WGT_D * 1000 / d_prod.SLAB_WGT_D ELSE 0 END as dailyIronUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.TOTAL_SCRAP_D * 1000 / d_prod.SLAB_WGT_D ELSE 0 END as dailyScrapUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.IRON_WGT_D + d_con.TOTAL_SCRAP_D) * 1000 / d_prod.SLAB_WGT_D ELSE 0 END as dailySteelIronConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BAIHUI_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyLimeUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BYS_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyDolomiteUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.QSBYS_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyLightDolomiteUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GMHJ_D / d_prod.SLAB_WGT_D ELSE 0 END as dailySimnUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GUITIE_D / d_prod.SLAB_WGT_D ELSE 0 END as dailySifeUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.YZ_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyKilnSlagUnitConsumption,
            0 as cumulativeFurnaceCount,
            0 as cumulativeSlabOutput,
            0 as cumulativeIronConsumption,
            0 as cumulativeScrapConsumption,
            0 as cumulativeIronUnitConsumption,
            0 as cumulativeScrapUnitConsumption,
            0 as cumulativeSteelIronConsumption,
            0 as cumulativeLimeUnitConsumption,
            0 as cumulativeDolomiteUnitConsumption,
            0 as cumulativeLightDolomiteUnitConsumption,
            0 as cumulativeSimnUnitConsumption,
            0 as cumulativeSifeUnitConsumption,
            0 as cumulativeKilnSlagUnitConsumption
        FROM daily_prod_l1_cte d_prod,
             monthly_prod_l1_cte m_prod,
             daily_consumption_l1_cte d_con,
             daily_furnace_l1_cte d_furnace
    </select>

    <!-- 从源表查询L2数据 -->
    <select id="selectL2FromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsDailyReporter">
        WITH
        -- 1. 二钢产量（日）
        daily_prod_l2_cte AS (
            SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_D
            FROM JGLGMES.SMES_B_BOF_RES A
            LEFT JOIN (
                SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
                FROM JGLGMES.smes_b_cutres
                GROUP BY SUBSTR(HEAT_ID, 0, 9)
            ) F ON A.HEAT_ID = F.HEAT_ID
            LEFT JOIN (
                SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
                FROM JGLGMES.SMES_B_CCMRES
                WHERE MAC_CODE IN ('2D312','2D313')
                GROUP BY HEAT_ID
            ) H ON A.HEAT_ID = H.HEAT_ID
            WHERE A.WORK_SHOP = 'L2'
              AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
        ),
        -- 2. 二钢日消耗数据
        daily_consumption_l2_cte AS (
            SELECT 
                NVL(SUM(CC.IRON_WGT), 0) AS IRON_WGT_D,
                NVL(SUM(DD.FG), 0) AS TOTAL_SCRAP_D,
                NVL(SUM(DD.BAIHUI), 0) AS BAIHUI_D,
                NVL(SUM(DD.QSBYS), 0) AS QSBYS_D,
                NVL(SUM(DD.BYS), 0) AS BYS_D,
                NVL(SUM(DD.SHS), 0) AS SHS_D,
                NVL(SUM(DD.GMHJ), 0) AS GMHJ_D,
                NVL(SUM(DD.GUITIE), 0) AS GUITIE_D,
                NVL(SUM(DD.YZ), 0) AS YZ_D
            FROM JGLGMES.SMES_B_CCMRES AA
            LEFT JOIN JGLGMES.SMES_B_BOF_RES CC ON AA.HEAT_ID = CC.HEAT_ID
            LEFT JOIN (
                SELECT HEAT_ID,
                       SUM(DECODE(MAT_CODE, '10401014', MAT_WGT / 1000, '10401015', MAT_WGT / 1000, '10704001', MAT_WGT / 1000, 0)) AS FG,
                       SUM(DECODE(MAT_CODE, '10201001', MAT_WGT, 0)) AS GUITIE,
                       SUM(DECODE(MAT_CODE, '10201006', MAT_WGT, 0)) AS GMHJ,
                       SUM(DECODE(MAT_CODE, '10501024', MAT_WGT, '10705001', MAT_WGT, 0)) AS BAIHUI,
                       SUM(DECODE(MAT_CODE, '10501002', MAT_WGT, '10705003', MAT_WGT, 0)) AS QSBYS,
                       SUM(DECODE(MAT_CODE, '10501015', MAT_WGT, '10501029', MAT_WGT, 0)) AS BYS,
                       SUM(DECODE(MAT_CODE, '10501035', MAT_WGT, '10501036', MAT_WGT, '10501012', MAT_WGT, 0)) AS SHS,
                       SUM(DECODE(MAT_CODE, '10701002', MAT_WGT, 0)) AS YZ
                FROM JGLGMES.SMES_B_MAT_USE
                GROUP BY HEAT_ID
            ) DD ON AA.HEAT_ID = DD.HEAT_ID
            WHERE AA.WORK_SHOP = 'L2'
              AND TRUNC(AA.STAR_CAST_TIM, 'DD') = TRUNC(#{date}, 'DD')
        ),
        -- 3. 二钢炉数（日）
        daily_furnace_l2_cte AS (
            SELECT COUNT(*) AS FURNACE_COUNT_D
            FROM JGLGMES.SMES_B_BOF_RES bof
            WHERE bof.WORK_SHOP = 'L2'
              AND bof.MAC_CODE IN ('2C011', '2C012')
              AND EXISTS (
                  SELECT 1
                  FROM JGLGMES.SMES_B_CCMRES ccm
                  WHERE ccm.heat_id = bof.HEAT_ID
                    AND ccm.WORK_SHOP = 'L2'
                    AND ccm.MAC_CODE IN ('2D312','2D313')
                    AND TRUNC(ccm.star_cast_tim, 'DD') = TRUNC(#{date}, 'DD')
              )
        )
        -- 最终二钢数据查询
        SELECT 
            #{date} as reportDate,
            'L2' as unitCode,
            '炼钢二厂' as unitName,
            d_furnace.FURNACE_COUNT_D as dailyFurnaceCount,
            d_prod.SLAB_WGT_D as dailySlabOutput,
            d_con.IRON_WGT_D as dailyIronConsumption,
            d_con.TOTAL_SCRAP_D as dailyScrapConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.IRON_WGT_D * 1000 / d_prod.SLAB_WGT_D ELSE 0 END as dailyIronUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.TOTAL_SCRAP_D * 1000 / d_prod.SLAB_WGT_D ELSE 0 END as dailyScrapUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.IRON_WGT_D + d_con.TOTAL_SCRAP_D) * 1000 / d_prod.SLAB_WGT_D ELSE 0 END as dailySteelIronConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BAIHUI_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyLimeUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BYS_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyDolomiteUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.QSBYS_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyLightDolomiteUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GMHJ_D / d_prod.SLAB_WGT_D ELSE 0 END as dailySimnUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GUITIE_D / d_prod.SLAB_WGT_D ELSE 0 END as dailySifeUnitConsumption,
            CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.YZ_D / d_prod.SLAB_WGT_D ELSE 0 END as dailyKilnSlagUnitConsumption,
            0 as cumulativeFurnaceCount,
            0 as cumulativeSlabOutput,
            0 as cumulativeIronConsumption,
            0 as cumulativeScrapConsumption,
            0 as cumulativeIronUnitConsumption,
            0 as cumulativeScrapUnitConsumption,
            0 as cumulativeSteelIronConsumption,
            0 as cumulativeLimeUnitConsumption,
            0 as cumulativeDolomiteUnitConsumption,
            0 as cumulativeLightDolomiteUnitConsumption,
            0 as cumulativeSimnUnitConsumption,
            0 as cumulativeSifeUnitConsumption,
            0 as cumulativeKilnSlagUnitConsumption
        FROM daily_prod_l2_cte d_prod,
             daily_consumption_l2_cte d_con,
             daily_furnace_l2_cte d_furnace
    </select>

    <!-- 根据日期从源表查询数据并插入到报表表 -->
    <insert id="insertFromSource" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_DAILY_REPORTER (
            REPORT_DATE, UNIT_CODE, UNIT_NAME,
            DAILY_FURNACE_COUNT, DAILY_SLAB_OUTPUT, DAILY_IRON_CONSUMPTION, DAILY_SCRAP_CONSUMPTION,
            DAILY_IRON_UNIT_CONSUMPTION, DAILY_SCRAP_UNIT_CONSUMPTION, DAILY_STEEL_IRON_CONSUMPTION,
            DAILY_LIME_UNIT_CONSUMPTION, DAILY_DOLOMITE_UNIT_CONSUMPTION, DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            DAILY_SIMN_UNIT_CONSUMPTION, DAILY_SIFE_UNIT_CONSUMPTION, DAILY_KILN_SLAG_UNIT_CONSUMPTION,
            CUMULATIVE_FURNACE_COUNT, CUMULATIVE_SLAB_OUTPUT, CUMULATIVE_IRON_CONSUMPTION, CUMULATIVE_SCRAP_CONSUMPTION,
            CUMULATIVE_IRON_UNIT_CONSUMPTION, CUMULATIVE_SCRAP_UNIT_CONSUMPTION, CUMULATIVE_STEEL_IRON_CONSUMPTION,
            CUMULATIVE_LIME_UNIT_CONSUMPTION, CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION, CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            CUMULATIVE_SIMN_UNIT_CONSUMPTION, CUMULATIVE_SIFE_UNIT_CONSUMPTION, CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
        )
        WITH
        -- 一钢产量（日）
        daily_prod_l1_cte AS (
            SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_D
            FROM JGLGMES.SMES_B_BOF_RES A
            LEFT JOIN (
                SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
                FROM JGLGMES.smes_b_cutres
                GROUP BY SUBSTR(HEAT_ID, 0, 9)
            ) F ON A.HEAT_ID = F.HEAT_ID
            LEFT JOIN (
                SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
                FROM JGLGMES.SMES_B_CCMRES
                WHERE MAC_CODE IN ('1D311', '1D313', '1D314')
                GROUP BY HEAT_ID
            ) H ON A.HEAT_ID = H.HEAT_ID
            WHERE A.WORK_SHOP = 'L1'
              AND A.MAC_CODE != '1C015'
              AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
        )
        SELECT 
            #{date},
            'L1',
            '炼钢一厂',
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        FROM dual
    </insert>

    <!-- 根据日期删除数据 -->
    <!-- 一钢数据插入 -->
    <insert id="insertFromSourceByL1" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_DAILY_REPORTER (
            REPORT_DATE, UNIT_CODE, UNIT_NAME,
            DAILY_FURNACE_COUNT, DAILY_SLAB_OUTPUT, DAILY_IRON_CONSUMPTION, DAILY_SCRAP_CONSUMPTION,
            DAILY_IRON_UNIT_CONSUMPTION, DAILY_SCRAP_UNIT_CONSUMPTION, DAILY_STEEL_IRON_CONSUMPTION,
            DAILY_LIME_UNIT_CONSUMPTION, DAILY_DOLOMITE_UNIT_CONSUMPTION, DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            DAILY_SIMN_UNIT_CONSUMPTION, DAILY_SIFE_UNIT_CONSUMPTION, DAILY_KILN_SLAG_UNIT_CONSUMPTION,
            CUMULATIVE_FURNACE_COUNT, CUMULATIVE_SLAB_OUTPUT, CUMULATIVE_IRON_CONSUMPTION, CUMULATIVE_SCRAP_CONSUMPTION,
            CUMULATIVE_IRON_UNIT_CONSUMPTION, CUMULATIVE_SCRAP_UNIT_CONSUMPTION, CUMULATIVE_STEEL_IRON_CONSUMPTION,
            CUMULATIVE_LIME_UNIT_CONSUMPTION, CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION, CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            CUMULATIVE_SIMN_UNIT_CONSUMPTION, CUMULATIVE_SIFE_UNIT_CONSUMPTION, CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
        )
        SELECT
            #{date},
            'L1',
            '炼钢一厂',
            (SELECT COUNT(*) FROM JGLGMES.SMES_B_BOF_RES bof
             WHERE bof.WORK_SHOP = 'L1'
               AND bof.MAC_CODE IN ('1C013', '1C014')
               AND EXISTS (SELECT 1 FROM JGLGMES.SMES_B_CCMRES ccm
                          WHERE ccm.heat_id = bof.HEAT_ID
                            AND ccm.WORK_SHOP = 'L1'
                            AND TRUNC(ccm.star_cast_tim, 'DD') = TRUNC(#{date})
                            AND ccm.MAC_CODE in ('1D311','1D313','1D314'))),
            (SELECT NVL(SUM(F.SLAB_WEIGHT), 0)
             FROM JGLGMES.SMES_B_BOF_RES A
             LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
                       FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID, 0, 9)) F ON A.HEAT_ID = F.HEAT_ID
             LEFT JOIN (SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
                       FROM JGLGMES.SMES_B_CCMRES WHERE MAC_CODE IN ('1D311', '1D313', '1D314')
                       GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
             WHERE A.WORK_SHOP = 'L1' AND A.MAC_CODE != '1C015'
               AND TRUNC(H.STAR_CAST_TIM, 'DD') = TRUNC(#{date})),
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
    </insert>

    <!-- 二钢数据插入 -->
    <insert id="insertFromSourceByL2" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_DAILY_REPORTER (
            REPORT_DATE, UNIT_CODE, UNIT_NAME,
            DAILY_FURNACE_COUNT, DAILY_SLAB_OUTPUT, DAILY_IRON_CONSUMPTION, DAILY_SCRAP_CONSUMPTION,
            DAILY_IRON_UNIT_CONSUMPTION, DAILY_SCRAP_UNIT_CONSUMPTION, DAILY_STEEL_IRON_CONSUMPTION,
            DAILY_LIME_UNIT_CONSUMPTION, DAILY_DOLOMITE_UNIT_CONSUMPTION, DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            DAILY_SIMN_UNIT_CONSUMPTION, DAILY_SIFE_UNIT_CONSUMPTION, DAILY_KILN_SLAG_UNIT_CONSUMPTION,
            CUMULATIVE_FURNACE_COUNT, CUMULATIVE_SLAB_OUTPUT, CUMULATIVE_IRON_CONSUMPTION, CUMULATIVE_SCRAP_CONSUMPTION,
            CUMULATIVE_IRON_UNIT_CONSUMPTION, CUMULATIVE_SCRAP_UNIT_CONSUMPTION, CUMULATIVE_STEEL_IRON_CONSUMPTION,
            CUMULATIVE_LIME_UNIT_CONSUMPTION, CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION, CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
            CUMULATIVE_SIMN_UNIT_CONSUMPTION, CUMULATIVE_SIFE_UNIT_CONSUMPTION, CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
        )
        SELECT
            #{date},
            'L2',
            '炼钢二厂',
            (SELECT COUNT(*) FROM JGLGMES.SMES_B_BOF_RES bof
             WHERE bof.WORK_SHOP = 'L2'
               AND bof.MAC_CODE IN ('2C011', '2C012')
               AND EXISTS (SELECT 1 FROM JGLGMES.SMES_B_CCMRES ccm
                          WHERE ccm.heat_id = bof.HEAT_ID
                            AND ccm.WORK_SHOP = 'L2'
                            AND ccm.MAC_CODE IN ('2D312','2D313')
                            AND TRUNC(ccm.star_cast_tim, 'DD') = TRUNC(#{date}))),
            (SELECT NVL(SUM(F.SLAB_WEIGHT), 0)
             FROM JGLGMES.SMES_B_BOF_RES A
             LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
                       FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID, 0, 9)) F ON A.HEAT_ID = F.HEAT_ID
             LEFT JOIN (SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
                       FROM JGLGMES.SMES_B_CCMRES WHERE MAC_CODE IN ('2D312','2D313')
                       GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
             WHERE A.WORK_SHOP = 'L2'
               AND TRUNC(H.STAR_CAST_TIM, 'DD') = TRUNC(#{date})),
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
    </insert>

    <delete id="deleteByDate" parameterType="java.util.Date">
        DELETE FROM JGLGMES.CARBON_LGMS_DAILY_REPORTER
        WHERE TRUNC(REPORT_DATE) = TRUNC(#{date})
    </delete>

    <!-- 清空表数据 -->
    <delete id="truncate">
        TRUNCATE TABLE JGLGMES.CARBON_LGMS_DAILY_REPORTER
    </delete>

</mapper>
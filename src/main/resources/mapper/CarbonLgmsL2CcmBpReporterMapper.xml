<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jggroup.fakereports.business.mapper.CarbonLgmsL2CcmBpReporterMapper">

    <!-- 批量插入板坯报表数据 -->
    <insert id="insertAll" parameterType="java.util.List">
        INSERT INTO JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER (
            HEAT_ID, CAST_ID, WORK_SHOP, P_DATE,
            GROUP1, CAST_SDL, HEAT_SDL, SHIFT, CAST_SEQ_N, PENSHUI, STL_WGT_WAIT,
            STL_GRD_CD, STL_COD, LADLE_ID, AUTO_OPEN_FLG,
            AVG_CAST_SPEED, TARGET_ARR_TMP, LADLE_ARR_TMP,
            LADLE_ARR_TIME, STAR_CAST_TIM, END_CAST_TIM,
            LD_ARR_NET_WGHT, LD_DEP_NET_WGHT, CAST_WGT, HEAT_SLAB_CNT,
            TD_NO, TUNDISH_TEMP_AVG1, TUNDISH_TEMP_AVG2,
            CASTING_POWDER_TYPE, COVERING_AGENT_TYPE, MAC_CODE,
            SLAB_CNT, SLAB_WGT, MEMO,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA, CCM_TI, CCM_CU, CCM_NI, CCM_B, CCM_N, CCM_CEQ,
            PART_ONE, PART_TWO,
            SPEED_ONE, SPEED_TWO,
            MOTEMP_ONE, MOTEMP_TWO,
            MODIF_ONE, MODIF_TWO,
            MOSURE_ONE, MOSURE_TWO,
            STRAND_NO1_BHZ, STRAND_NO2_BHZ, STRAND_NO1_FGJ, STRAND_NO2_FGJ
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.heatId}, #{item.castId}, #{item.workShop}, #{item.pDate},
                #{item.group1}, #{item.castSdl}, #{item.heatSdl}, #{item.shift}, #{item.castSeqN}, #{item.penshui}, #{item.stlWgtWait},
                #{item.stlGrdCd}, #{item.stlCod}, #{item.ladleId}, #{item.autoOpenFlg},
                #{item.avgCastSpeed}, #{item.targetArrTmp}, #{item.ladleArrTmp},
                #{item.ladleArrTime}, #{item.starCastTim}, #{item.endCastTim},
                #{item.ldArrNetWght}, #{item.ldDepNetWght}, #{item.castWgt}, #{item.heatSlabCnt},
                #{item.tdNo}, #{item.tundishTempAvg1}, #{item.tundishTempAvg2},
                #{item.castingPowderType}, #{item.coveringAgentType}, #{item.macCode},
                #{item.slabCnt}, #{item.slabWgt}, #{item.memo},
                #{item.ccmC}, #{item.ccmMn}, #{item.ccmSi}, #{item.ccmS}, #{item.ccmP}, #{item.ccmV}, #{item.ccmAls}, #{item.ccmAlt}, #{item.ccmCa}, #{item.ccmTi}, #{item.ccmCu}, #{item.ccmNi}, #{item.ccmB}, #{item.ccmN}, #{item.ccmCeq},
                #{item.partOne}, #{item.partTwo},
                #{item.speedOne}, #{item.speedTwo},
                #{item.motempOne}, #{item.motempTwo},
                #{item.modifOne}, #{item.modifTwo},
                #{item.mosureOne}, #{item.mosureTwo},
                #{item.strandNo1Bhz}, #{item.strandNo2Bhz}, #{item.strandNo1Fgj}, #{item.strandNo2Fgj}
            )
        </foreach>
    </insert>

    <!-- 从源表查询数据 -->
    <select id="selectFromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsL2CcmBpReporter">
        SELECT
            C.HEAT_ID as heatId,
            C.CAST_ID as castId,
            'L2' as workShop,
            C.CCM_DATE as pDate,
            C.GROUP1 as group1,
            C.CAST_SDL as castSdl,
            C.HEAT_SDL as heatSdl,
            C.SHIFT as shift,
            C.CAST_SEQ_N as castSeqN,
            C.PENSHUI as penshui,
            C.STL_WGT_WAIT as stlWgtWait,
            C.STL_GRD_CD as stlGrdCd,
            C.STL_COD as stlCod,
            C.LADLE_ID as ladleId,
            C.AUTO_OPEN_FLG as autoOpenFlg,
            C.AVG_CAST_SPEED as avgCastSpeed,
            C.TARGET_ARR_TMP as targetArrTmp,
            C.LADLE_ARR_TMP as ladleArrTmp,
            C.LADLE_ARR_TIME as ladleArrTime,
            C.STAR_CAST_TIM as starCastTim,
            C.END_CAST_TIM as endCastTim,
            C.LD_ARR_NET_WGHT as ldArrNetWght,
            C.LD_DEP_NET_WGHT as ldDepNetWght,
            C.CAST_WGT as castWgt,
            C.HEAT_SLAB_CNT as heatSlabCnt,
            C.TD_NO as tdNo,
            C.TUNDISH_TEMP_AVG1 as tundishTempAvg1,
            C.TUNDISH_TEMP_AVG2 as tundishTempAvg2,
            C.CASTING_POWDER_TYPE as castingPowderType,
            C.COVERING_AGENT_TYPE as coveringAgentType,
            C.MAC_CODE as macCode,
            D.SLAB_CNT as slabCnt,
            D.SLAB_WGT as slabWgt,
            C.MEMO as memo,
            B.C as ccmC,
            B.Mn as ccmMn,
            B.Si as ccmSi,
            B.S as ccmS,
            B.P as ccmP,
            B.V as ccmV,
            B.ALS as ccmAls,
            B.AL as ccmAlt,
            B.CA as ccmCa,
            B.TI as ccmTi,
            B.CU as ccmCu,
            B.NI as ccmNi,
            B.B as ccmB,
            B.N as ccmN,
            B.CEQ as ccmCeq,
            AA.PART_ONE as partOne,
            AA.PART_TWO as partTwo,
            AA.SPEED_ONE as speedOne,
            AA.SPEED_TWO as speedTwo,
            BB.MOTEMP_ONE as motempOne,
            BB.MOTEMP_TWO as motempTwo,
            BB.MODIF_ONE as modifOne,
            BB.MODIF_TWO as modifTwo,
            BB.MOSURE_ONE as mosureOne,
            BB.MOSURE_TWO as mosureTwo,
            GG.STRAND_NO1_BHZ as strandNo1Bhz,
            GG.STRAND_NO2_BHZ as strandNo2Bhz,
            GG.STRAND_NO1_FGJ as strandNo1Fgj,
            GG.STRAND_NO2_FGJ as strandNo2Fgj
        FROM JGLGMES.SMES_B_CCMRES C
        LEFT JOIN (
            SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID,
                   TREAT_NO,
                   COUNT(1) SLAB_CNT,
                   SUM(SLAB_WEIGHT) SLAB_WGT
            FROM JGLGMES.SMES_B_CUTRES
            GROUP BY SUBSTR(HEAT_ID,0,9), TREAT_NO
        ) D ON C.HEAT_ID = D.HEAT_ID AND C.TREAT_NO = D.TREAT_NO
        LEFT JOIN (
            SELECT *
            FROM JGLGMES.SMES_B_che_steel a
            WHERE a.SEQ_NO IN (
                SELECT MAX(b.SEQ_NO)
                FROM JGLGMES.SMES_B_che_steel b
                WHERE a.HEAT_ID = b.HEAT_ID
                GROUP BY SUBSTR(SAMPLETYPE, 0, 1)
            )
            AND sampletype LIKE 'C%'
        ) B ON C.HEAT_ID = B.HEAT_ID
        LEFT JOIN (
            SELECT HEAT_ID,
                   TREAT_NO,
                   MAX(DECODE(STRAND_NO, '1', MOULD_NO)) PART_ONE,
                   MAX(DECODE(STRAND_NO, '2', MOULD_NO)) PART_TWO,
                   MAX(DECODE(STRAND_NO, '1', SPEEDVALUE)) SPEED_ONE,
                   MAX(DECODE(STRAND_NO, '2', SPEEDVALUE)) SPEED_TWO
            FROM JGLGMES.SMES_B_CCMRES_STREAM
            GROUP BY HEAT_ID, TREAT_NO
        ) AA ON C.HEAT_ID = AA.HEAT_ID AND C.TREAT_NO = AA.TREAT_NO
        LEFT JOIN (
            SELECT HEAT_ID,
                   TREAT_NO,
                   MAX(DECODE(STRAND_NO, '1', MOULD_ACTVALUE)) MOTEMP_ONE,
                   MAX(DECODE(STRAND_NO, '2', MOULD_ACTVALUE)) MOTEMP_TWO,
                   MAX(DECODE(STRAND_NO, '1', MLD_WAT_T)) MODIF_ONE,
                   MAX(DECODE(STRAND_NO, '2', MLD_WAT_T)) MODIF_TWO,
                   MAX(DECODE(STRAND_NO, '1', MOULDWATERDELTATEMP)) MOSURE_ONE,
                   MAX(DECODE(STRAND_NO, '2', MOULDWATERDELTATEMP)) MOSURE_TWO
            FROM JGLGMES.SMES_B_CCM_PAR
            GROUP BY HEAT_ID, TREAT_NO
        ) BB ON C.HEAT_ID = BB.HEAT_ID AND C.TREAT_NO = BB.TREAT_NO
        LEFT JOIN (
            SELECT *
            FROM (
                SELECT *
                FROM (
                    SELECT t.heat_id,
                           t.treat_no,
                           t.strand_no,
                           DECODE(t.bhz_seq, bb.seq_no, bb.part_id, '') bhz,
                           DECODE(t.fgj_seq, cc.seq_no, cc.part_id, '') fgj
                    FROM JGLGMES.SMES_B_CCMRES_STREAM t,
                         (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%02') bb,
                         (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%03') cc
                    WHERE t.bhz_seq = bb.seq_no(+)
                      AND t.fgj_seq = cc.seq_no(+)
                ) AAA
            )
            PIVOT(MAX(BHZ) BHZ, MAX(FGJ) FGJ
                  FOR strand_no IN('1' AS strand_no1,
                                   '2' AS strand_no2))
        ) GG ON C.HEAT_ID = GG.HEAT_ID AND C.TREAT_NO = GG.TREAT_NO
        WHERE C.WORK_SHOP = 'L2'
          AND TO_CHAR(C.STAR_CAST_TIM, 'YYYY-MM-DD') = TO_CHAR(#{date}, 'YYYY-MM-DD')
          AND C.MAC_CODE IN ('2D312','2D313')
          AND C.HEAT_ID NOT LIKE 'L5%'
        ORDER BY C.STAR_CAST_TIM ASC
    </select>

    <!-- 根据日期从源表查询数据并插入到报表表 -->
    <insert id="insertFromSource" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER (
            HEAT_ID, CAST_ID, WORK_SHOP, P_DATE,
            GROUP1, CAST_SDL, HEAT_SDL, SHIFT, CAST_SEQ_N, PENSHUI, STL_WGT_WAIT,
            STL_GRD_CD, STL_COD, LADLE_ID, AUTO_OPEN_FLG,
            AVG_CAST_SPEED, TARGET_ARR_TMP, LADLE_ARR_TMP,
            LADLE_ARR_TIME, STAR_CAST_TIM, END_CAST_TIM,
            LD_ARR_NET_WGHT, LD_DEP_NET_WGHT, CAST_WGT, HEAT_SLAB_CNT,
            TD_NO, TUNDISH_TEMP_AVG1, TUNDISH_TEMP_AVG2,
            CASTING_POWDER_TYPE, COVERING_AGENT_TYPE, MAC_CODE,
            SLAB_CNT, SLAB_WGT, MEMO,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA, CCM_TI, CCM_CU, CCM_NI, CCM_B, CCM_N, CCM_CEQ,
            PART_ONE, PART_TWO,
            SPEED_ONE, SPEED_TWO,
            MOTEMP_ONE, MOTEMP_TWO,
            MODIF_ONE, MODIF_TWO,
            MOSURE_ONE, MOSURE_TWO,
            STRAND_NO1_BHZ, STRAND_NO2_BHZ, STRAND_NO1_FGJ, STRAND_NO2_FGJ
        )
        SELECT
            C.HEAT_ID,
            C.CAST_ID,
            'L2',
            C.CCM_DATE,
            C.GROUP1,
            C.CAST_SDL,
            C.HEAT_SDL,
            C.SHIFT,
            C.CAST_SEQ_N,
            C.PENSHUI,
            C.STL_WGT_WAIT,
            C.STL_GRD_CD,
            C.STL_COD,
            C.LADLE_ID,
            C.AUTO_OPEN_FLG,
            C.AVG_CAST_SPEED,
            C.TARGET_ARR_TMP,
            C.LADLE_ARR_TMP,
            C.LADLE_ARR_TIME,
            C.STAR_CAST_TIM,
            C.END_CAST_TIM,
            C.LD_ARR_NET_WGHT,
            C.LD_DEP_NET_WGHT,
            C.CAST_WGT,
            C.HEAT_SLAB_CNT,
            C.TD_NO,
            C.TUNDISH_TEMP_AVG1,
            C.TUNDISH_TEMP_AVG2,
            C.CASTING_POWDER_TYPE,
            C.COVERING_AGENT_TYPE,
            C.MAC_CODE,
            D.SLAB_CNT,
            D.SLAB_WGT,
            C.MEMO,
            B.C,
            B.Mn,
            B.Si,
            B.S,
            B.P,
            B.V,
            B.ALS,
            B.AL,
            B.CA,
            B.TI,
            B.CU,
            B.NI,
            B.B,
            B.N,
            B.CEQ,
            AA.PART_ONE,
            AA.PART_TWO,
            AA.SPEED_ONE,
            AA.SPEED_TWO,
            BB.MOTEMP_ONE,
            BB.MOTEMP_TWO,
            BB.MODIF_ONE,
            BB.MODIF_TWO,
            BB.MOSURE_ONE,
            BB.MOSURE_TWO,
            GG.STRAND_NO1_BHZ,
            GG.STRAND_NO2_BHZ,
            GG.STRAND_NO1_FGJ,
            GG.STRAND_NO2_FGJ
        FROM JGLGMES.SMES_B_CCMRES C,
             (SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID,
                     TREAT_NO,
                     COUNT(1) SLAB_CNT,
                     SUM(SLAB_WEIGHT) SLAB_WGT
              FROM JGLGMES.SMES_B_CUTRES
              GROUP BY SUBSTR(HEAT_ID,0,9), TREAT_NO) D,
             (SELECT *
              FROM JGLGMES.SMES_B_che_steel a
              WHERE a.SEQ_NO IN (SELECT MAX(b.SEQ_NO)
                                 FROM JGLGMES.SMES_B_che_steel b
                                 WHERE a.HEAT_ID = b.HEAT_ID
                                 GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
                AND sampletype LIKE 'C%') B,
             (SELECT HEAT_ID,
                     TREAT_NO,
                     MAX(DECODE(STRAND_NO, '1', MOULD_NO)) PART_ONE,
                     MAX(DECODE(STRAND_NO, '2', MOULD_NO)) PART_TWO,
                     MAX(DECODE(STRAND_NO, '1', SPEEDVALUE)) SPEED_ONE,
                     MAX(DECODE(STRAND_NO, '2', SPEEDVALUE)) SPEED_TWO
              FROM JGLGMES.SMES_B_CCMRES_STREAM
              GROUP BY HEAT_ID, TREAT_NO) AA,
             (SELECT HEAT_ID,
                     TREAT_NO,
                     MAX(DECODE(STRAND_NO, '1', MOULD_ACTVALUE)) MOTEMP_ONE,
                     MAX(DECODE(STRAND_NO, '2', MOULD_ACTVALUE)) MOTEMP_TWO,
                     MAX(DECODE(STRAND_NO, '1', MLD_WAT_T)) MODIF_ONE,
                     MAX(DECODE(STRAND_NO, '2', MLD_WAT_T)) MODIF_TWO,
                     MAX(DECODE(STRAND_NO, '1', MOULDWATERDELTATEMP)) MOSURE_ONE,
                     MAX(DECODE(STRAND_NO, '2', MOULDWATERDELTATEMP)) MOSURE_TWO
              FROM JGLGMES.SMES_B_CCM_PAR
              GROUP BY HEAT_ID, TREAT_NO) BB,
             (SELECT *
              FROM (
                  SELECT *
                  FROM (
                      SELECT t.heat_id,
                             t.treat_no,
                             t.strand_no,
                             DECODE(t.bhz_seq, bb.seq_no, bb.part_id, '') bhz,
                             DECODE(t.fgj_seq, cc.seq_no, cc.part_id, '') fgj
                      FROM JGLGMES.SMES_B_CCMRES_STREAM t,
                           (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%02') bb,
                           (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%03') cc
                      WHERE t.bhz_seq = bb.seq_no(+)
                        AND t.fgj_seq = cc.seq_no(+)
                  ) AAA
              )
              PIVOT(MAX(BHZ) BHZ, MAX(FGJ) FGJ
                    FOR strand_no IN('1' AS strand_no1,
                                     '2' AS strand_no2))) GG
        WHERE C.HEAT_ID = D.HEAT_ID(+)
          AND C.TREAT_NO = D.TREAT_NO(+)
          AND C.HEAT_ID = B.HEAT_ID(+)
          AND C.HEAT_ID = AA.HEAT_ID(+)
          AND C.TREAT_NO = AA.TREAT_NO(+)
          AND C.HEAT_ID = BB.HEAT_ID(+)
          AND C.TREAT_NO = BB.TREAT_NO(+)
          AND C.HEAT_ID = GG.HEAT_ID(+)
          AND C.TREAT_NO = GG.TREAT_NO(+)
          AND C.WORK_SHOP = 'L2'
          AND TO_CHAR(C.STAR_CAST_TIM, 'YYYY-MM-DD') = TO_CHAR(#{date}, 'YYYY-MM-DD')
          AND C.MAC_CODE IN ('2D312','2D313')
          AND C.HEAT_ID NOT LIKE 'L5%'
        ORDER BY C.STAR_CAST_TIM ASC
    </insert>

    <!-- 根据日期删除数据 -->
    <delete id="deleteByDate" parameterType="java.util.Date">
        DELETE FROM JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER
        WHERE TRUNC(P_DATE) = TRUNC(#{date})
    </delete>

    <!-- 清空表数据 -->
    <delete id="truncate">
        TRUNCATE TABLE JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER
    </delete>

</mapper>

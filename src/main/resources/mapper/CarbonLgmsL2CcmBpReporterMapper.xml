<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jggroup.fakereports.business.mapper.CarbonLgmsL2CcmBpReporterMapper">

    <!-- 批量插入板坯报表数据 -->
    <insert id="insertAll" parameterType="java.util.List">
        INSERT INTO JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER (
            HEAT_ID, TREAT_NO, WORK_SHOP, P_DATE,
            CCM_MAC_CODE, CCM_CAST_ID, CCM_CAST_SEQ_N, CCM_LADLE_ID, CCM_AUTO_OPEN_FLG,
            STL_GRD_CD, STL_COD, HEAT_SDL, CAST_SDL, MAT_QUL_CD,
            CCM_GROUP, CCM_SHIFT, CCM_PENSHUI,
            CCM_LADLE_ARR_TIME, CCM_STAR_CAST_TIM, CCM_END_CAST_TIM, CCM_LD_DEP_TIM, CCM_YAG_TIME, CCM_TADE_TIME,
            CCM_TARGET_ARR_TMP, CCM_LADLE_ARR_TMP, CCM_TUNDISH_TEMP_AVG1, CCM_TUNDISH_TEMP_AVG2, CCM_TUNDISH_TEMP_AVG3, CCM_TUNDISH_TEMP_AVGR1, CCM_TUNDISH_TEMP_AVGR2, CCM_TUNDISH_TEMP_AVGR3,
            CCM_LD_ARR_NET_WGHT, CCM_LD_DEP_NET_WGHT, CCM_CAST_WGT, STL_WGT_WAIT,
            CCM_TD_NO, CCM_TD_NO2, CCM_MOULD_NO, CCM_MOULD_NO2, AVG_CAST_SPEED, ONESPEED, TWOSPEED,
            DANLIU, SHUANGLIU,
            CUT_SEQ_NO, CUT_SLAB_WEIGHT,
            CASTING_POWDER_TYPE, COVERING_AGENT_TYPE, BANPI_ZHONGBAO_FUGAIJI, BANPI_JIANXING_ZHONGBAO_FUGAIJI, BANPI_QUYANGQI, BANPI_BAOHUZHA, BANPI_DABAO_TAOGUAN, BANPI_SAIBANG, PINZHONGGANG_BAOHUZHA, BANPI_SHUIKOU, BANPI_CEWENGUAN, BANPI_GANGBAO_FUGAIJI,
            PART_ID_ONE, ONE_BHZ_FACTORY, PART_ID_TWO, TWO_BHZ_FACTORY,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA, CCM_TI, CCM_CU, CCM_NI, CCM_MO, CCM_B, CCM_N,
            STRAND1_SPEED5, STRAND1_SPEED10, STRAND1_SPEED15, STRAND1_SPEED20, STRAND1_SPEED25,
            STRAND2_SPEED5, STRAND2_SPEED10, STRAND2_SPEED15, STRAND2_SPEED20, STRAND2_SPEED25,
            STRAND1_MLD_WAT_T_IN, STRAND1_MLD_WAT_T_OUT, STRAND1_MOULDWATERDELTATEMP, STRAND1_MLD_WAT_P, STRAND1_M_FT301_DIS, STRAND1_M_FT304_DIS, STRAND1_SECOND_TABLE_NAME, STRAND1_SEC_COOL_WAT_GENERAL_T, STRAND1_M_PT400_PRESSURE, STRAND1_F_ACTUAL, STRAND1_H_ACTUAL, STRAND1_MLCSSTEELLEVEL,
            STRAND2_MLD_WAT_T_IN, STRAND2_MLD_WAT_T_OUT, STRAND2_MOULDWATERDELTATEMP, STRAND2_MLD_WAT_P, STRAND2_M_FT301_DIS, STRAND2_M_FT304_DIS, STRAND2_SECOND_TABLE_NAME, STRAND2_SEC_COOL_WAT_GENERAL_T, STRAND2_M_PT400_PRESSURE, STRAND2_F_ACTUAL, STRAND2_H_ACTUAL, STRAND2_MLCSSTEELLEVEL,
            MEMO2, CCM_MEMO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.heatId}, #{item.treatNo}, #{item.workShop}, #{item.pDate},
                #{item.ccmMacCode}, #{item.ccmCastId}, #{item.ccmCastSeqN}, #{item.ccmLadleId}, #{item.ccmAutoOpenFlg},
                #{item.stlGrdCd}, #{item.stlCod}, #{item.heatSdl}, #{item.castSdl}, #{item.matQulCd},
                #{item.ccmGroup}, #{item.ccmShift}, #{item.ccmPenshui},
                #{item.ccmLadleArrTime}, #{item.ccmStarCastTim}, #{item.ccmEndCastTim}, #{item.ccmLdDepTim}, #{item.ccmYagTime}, #{item.ccmTadeTime},
                #{item.ccmTargetArrTmp}, #{item.ccmLadleArrTmp}, #{item.ccmTundishTempAvg1}, #{item.ccmTundishTempAvg2}, #{item.ccmTundishTempAvg3}, #{item.ccmTundishTempAvgr1}, #{item.ccmTundishTempAvgr2}, #{item.ccmTundishTempAvgr3},
                #{item.ccmLdArrNetWght}, #{item.ccmLdDepNetWght}, #{item.ccmCastWgt}, #{item.stlWgtWait},
                #{item.ccmTdNo}, #{item.ccmTdNo2}, #{item.ccmMouldNo}, #{item.ccmMouldNo2}, #{item.avgCastSpeed}, #{item.onespeed}, #{item.twospeed},
                #{item.danliu}, #{item.shuangliu},
                #{item.cutSeqNo}, #{item.cutSlabWeight},
                #{item.castingPowderType}, #{item.coveringAgentType}, #{item.banpiZhongbaoFugaiji}, #{item.banpiJianxingZhongbaoFugaiji}, #{item.banpiQuyangqi}, #{item.banpiBaohuzha}, #{item.banpiDabaoTaoguan}, #{item.banpiSaibang}, #{item.pinzhonggangBaohuzha}, #{item.banpiShuikou}, #{item.banpiCewenguan}, #{item.banpiGangbaoFugaiji},
                #{item.partIdOne}, #{item.oneBhzFactory}, #{item.partIdTwo}, #{item.twoBhzFactory},
                #{item.ccmC}, #{item.ccmMn}, #{item.ccmSi}, #{item.ccmS}, #{item.ccmP}, #{item.ccmV}, #{item.ccmAls}, #{item.ccmAlt}, #{item.ccmCa}, #{item.ccmTi}, #{item.ccmCu}, #{item.ccmNi}, #{item.ccmMo}, #{item.ccmB}, #{item.ccmN},
                #{item.strand1Speed5}, #{item.strand1Speed10}, #{item.strand1Speed15}, #{item.strand1Speed20}, #{item.strand1Speed25},
                #{item.strand2Speed5}, #{item.strand2Speed10}, #{item.strand2Speed15}, #{item.strand2Speed20}, #{item.strand2Speed25},
                #{item.strand1MldWatTIn}, #{item.strand1MldWatTOut}, #{item.strand1Mouldwaterdeltatemp}, #{item.strand1MldWatP}, #{item.strand1MFt301Dis}, #{item.strand1MFt304Dis}, #{item.strand1SecondTableName}, #{item.strand1SecCoolWatGeneralT}, #{item.strand1MPt400Pressure}, #{item.strand1FActual}, #{item.strand1HActual}, #{item.strand1Mlcssteellevel},
                #{item.strand2MldWatTIn}, #{item.strand2MldWatTOut}, #{item.strand2Mouldwaterdeltatemp}, #{item.strand2MldWatP}, #{item.strand2MFt301Dis}, #{item.strand2MFt304Dis}, #{item.strand2SecondTableName}, #{item.strand2SecCoolWatGeneralT}, #{item.strand2MPt400Pressure}, #{item.strand2FActual}, #{item.strand2HActual}, #{item.strand2Mlcssteellevel},
                #{item.memo2}, #{item.ccmMemo}
            )
        </foreach>
    </insert>

    <!-- 从源表查询数据 -->
    <select id="selectFromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsL2CcmBpReporter">
        SELECT
            C.HEAT_ID as heatId,
            C.CAST_ID as castId,
            'L2' as workShop,
            C.CCM_DATE as pDate,
            C.GROUP1 as group1,
            C.CAST_SDL as castSdl,
            C.HEAT_SDL as heatSdl,
            C.SHIFT as shift,
            C.CAST_SEQ_N as castSeqN,
            C.PENSHUI as penshui,
            C.STL_WGT_WAIT as stlWgtWait,
            C.STL_GRD_CD as stlGrdCd,
            C.STL_COD as stlCod,
            C.LADLE_ID as ladleId,
            C.AUTO_OPEN_FLG as autoOpenFlg,
            C.AVG_CAST_SPEED as avgCastSpeed,
            C.TARGET_ARR_TMP as targetArrTmp,
            C.LADLE_ARR_TMP as ladleArrTmp,
            C.LADLE_ARR_TIME as ladleArrTime,
            C.STAR_CAST_TIM as starCastTim,
            C.END_CAST_TIM as endCastTim,
            C.LD_ARR_NET_WGHT as ldArrNetWght,
            C.LD_DEP_NET_WGHT as ldDepNetWght,
            C.CAST_WGT as castWgt,
            C.HEAT_SLAB_CNT as heatSlabCnt,
            C.TD_NO as tdNo,
            C.TUNDISH_TEMP_AVG1 as tundishTempAvg1,
            C.TUNDISH_TEMP_AVG2 as tundishTempAvg2,
            C.CASTING_POWDER_TYPE as castingPowderType,
            C.COVERING_AGENT_TYPE as coveringAgentType,
            C.MAC_CODE as macCode,
            D.SLAB_CNT as slabCnt,
            D.SLAB_WGT as slabWgt,
            C.MEMO as memo,
            B.C as ccmC,
            B.Mn as ccmMn,
            B.Si as ccmSi,
            B.S as ccmS,
            B.P as ccmP,
            B.V as ccmV,
            B.ALS as ccmAls,
            B.AL as ccmAlt,
            B.CA as ccmCa,
            B.TI as ccmTi,
            B.CU as ccmCu,
            B.NI as ccmNi,
            B.B as ccmB,
            B.N as ccmN,
            B.CEQ as ccmCeq,
            AA.PART_ONE as partOne,
            AA.PART_TWO as partTwo,
            AA.SPEED_ONE as speedOne,
            AA.SPEED_TWO as speedTwo,
            BB.MOTEMP_ONE as motempOne,
            BB.MOTEMP_TWO as motempTwo,
            BB.MODIF_ONE as modifOne,
            BB.MODIF_TWO as modifTwo,
            BB.MOSURE_ONE as mosureOne,
            BB.MOSURE_TWO as mosureTwo,
            GG.STRAND_NO1_BHZ as strandNo1Bhz,
            GG.STRAND_NO2_BHZ as strandNo2Bhz,
            GG.STRAND_NO1_FGJ as strandNo1Fgj,
            GG.STRAND_NO2_FGJ as strandNo2Fgj
        FROM JGLGMES.SMES_B_CCMRES C
        LEFT JOIN (
            SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID,
                   TREAT_NO,
                   COUNT(1) SLAB_CNT,
                   SUM(SLAB_WEIGHT) SLAB_WGT
            FROM JGLGMES.SMES_B_CUTRES
            GROUP BY SUBSTR(HEAT_ID,0,9), TREAT_NO
        ) D ON C.HEAT_ID = D.HEAT_ID AND C.TREAT_NO = D.TREAT_NO
        LEFT JOIN (
            SELECT *
            FROM JGLGMES.SMES_B_che_steel a
            WHERE a.SEQ_NO IN (
                SELECT MAX(b.SEQ_NO)
                FROM JGLGMES.SMES_B_che_steel b
                WHERE a.HEAT_ID = b.HEAT_ID
                GROUP BY SUBSTR(SAMPLETYPE, 0, 1)
            )
            AND sampletype LIKE 'C%'
        ) B ON C.HEAT_ID = B.HEAT_ID
        LEFT JOIN (
            SELECT HEAT_ID,
                   TREAT_NO,
                   MAX(DECODE(STRAND_NO, '1', MOULD_NO)) PART_ONE,
                   MAX(DECODE(STRAND_NO, '2', MOULD_NO)) PART_TWO,
                   MAX(DECODE(STRAND_NO, '1', SPEEDVALUE)) SPEED_ONE,
                   MAX(DECODE(STRAND_NO, '2', SPEEDVALUE)) SPEED_TWO
            FROM JGLGMES.SMES_B_CCMRES_STREAM
            GROUP BY HEAT_ID, TREAT_NO
        ) AA ON C.HEAT_ID = AA.HEAT_ID AND C.TREAT_NO = AA.TREAT_NO
        LEFT JOIN (
            SELECT HEAT_ID,
                   TREAT_NO,
                   MAX(DECODE(STRAND_NO, '1', MOULD_ACTVALUE)) MOTEMP_ONE,
                   MAX(DECODE(STRAND_NO, '2', MOULD_ACTVALUE)) MOTEMP_TWO,
                   MAX(DECODE(STRAND_NO, '1', MLD_WAT_T)) MODIF_ONE,
                   MAX(DECODE(STRAND_NO, '2', MLD_WAT_T)) MODIF_TWO,
                   MAX(DECODE(STRAND_NO, '1', MOULDWATERDELTATEMP)) MOSURE_ONE,
                   MAX(DECODE(STRAND_NO, '2', MOULDWATERDELTATEMP)) MOSURE_TWO
            FROM JGLGMES.SMES_B_CCM_PAR
            GROUP BY HEAT_ID, TREAT_NO
        ) BB ON C.HEAT_ID = BB.HEAT_ID AND C.TREAT_NO = BB.TREAT_NO
        LEFT JOIN (
            SELECT *
            FROM (
                SELECT *
                FROM (
                    SELECT t.heat_id,
                           t.treat_no,
                           t.strand_no,
                           DECODE(t.bhz_seq, bb.seq_no, bb.part_id, '') bhz,
                           DECODE(t.fgj_seq, cc.seq_no, cc.part_id, '') fgj
                    FROM JGLGMES.SMES_B_CCMRES_STREAM t,
                         (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%02') bb,
                         (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%03') cc
                    WHERE t.bhz_seq = bb.seq_no(+)
                      AND t.fgj_seq = cc.seq_no(+)
                ) AAA
            )
            PIVOT(MAX(BHZ) BHZ, MAX(FGJ) FGJ
                  FOR strand_no IN('1' AS strand_no1,
                                   '2' AS strand_no2))
        ) GG ON C.HEAT_ID = GG.HEAT_ID AND C.TREAT_NO = GG.TREAT_NO
        WHERE C.WORK_SHOP = 'L2'
          AND TO_CHAR(C.STAR_CAST_TIM, 'YYYY-MM-DD') = TO_CHAR(#{date}, 'YYYY-MM-DD')
          AND C.MAC_CODE IN ('2D312','2D313')
          AND C.HEAT_ID NOT LIKE 'L5%'
        ORDER BY C.STAR_CAST_TIM ASC
    </select>

    <!-- 根据日期从源表查询数据并插入到报表表 -->
    <insert id="insertFromSource" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER (
            HEAT_ID, TREAT_NO, WORK_SHOP, P_DATE,
            CCM_MAC_CODE, CCM_CAST_ID, CCM_CAST_SEQ_N, CCM_LADLE_ID, CCM_AUTO_OPEN_FLG,
            STL_GRD_CD, STL_COD, HEAT_SDL, CAST_SDL, MAT_QUL_CD,
            CCM_GROUP, CCM_SHIFT, CCM_PENSHUI,
            CCM_LADLE_ARR_TIME, CCM_STAR_CAST_TIM, CCM_END_CAST_TIM, CCM_LD_DEP_TIM, CCM_YAG_TIME, CCM_TADE_TIME,
            CCM_TARGET_ARR_TMP, CCM_LADLE_ARR_TMP, CCM_TUNDISH_TEMP_AVG1, CCM_TUNDISH_TEMP_AVG2, CCM_TUNDISH_TEMP_AVG3, CCM_TUNDISH_TEMP_AVGR1, CCM_TUNDISH_TEMP_AVGR2, CCM_TUNDISH_TEMP_AVGR3,
            CCM_LD_ARR_NET_WGHT, CCM_LD_DEP_NET_WGHT, CCM_CAST_WGT, STL_WGT_WAIT,
            CCM_TD_NO, CCM_TD_NO2, CCM_MOULD_NO, CCM_MOULD_NO2, AVG_CAST_SPEED, ONESPEED, TWOSPEED,
            DANLIU, SHUANGLIU,
            CUT_SEQ_NO, CUT_SLAB_WEIGHT,
            CASTING_POWDER_TYPE, COVERING_AGENT_TYPE, BANPI_ZHONGBAO_FUGAIJI, BANPI_JIANXING_ZHONGBAO_FUGAIJI, BANPI_QUYANGQI, BANPI_BAOHUZHA, BANPI_DABAO_TAOGUAN, BANPI_SAIBANG, PINZHONGGANG_BAOHUZHA, BANPI_SHUIKOU, BANPI_CEWENGUAN, BANPI_GANGBAO_FUGAIJI,
            PART_ID_ONE, ONE_BHZ_FACTORY, PART_ID_TWO, TWO_BHZ_FACTORY,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA, CCM_TI, CCM_CU, CCM_NI, CCM_MO, CCM_B, CCM_N,
            STRAND1_SPEED5, STRAND1_SPEED10, STRAND1_SPEED15, STRAND1_SPEED20, STRAND1_SPEED25,
            STRAND2_SPEED5, STRAND2_SPEED10, STRAND2_SPEED15, STRAND2_SPEED20, STRAND2_SPEED25,
            STRAND1_MLD_WAT_T_IN, STRAND1_MLD_WAT_T_OUT, STRAND1_MOULDWATERDELTATEMP, STRAND1_MLD_WAT_P, STRAND1_M_FT301_DIS, STRAND1_M_FT304_DIS, STRAND1_SECOND_TABLE_NAME, STRAND1_SEC_COOL_WAT_GENERAL_T, STRAND1_M_PT400_PRESSURE, STRAND1_F_ACTUAL, STRAND1_H_ACTUAL, STRAND1_MLCSSTEELLEVEL,
            STRAND2_MLD_WAT_T_IN, STRAND2_MLD_WAT_T_OUT, STRAND2_MOULDWATERDELTATEMP, STRAND2_MLD_WAT_P, STRAND2_M_FT301_DIS, STRAND2_M_FT304_DIS, STRAND2_SECOND_TABLE_NAME, STRAND2_SEC_COOL_WAT_GENERAL_T, STRAND2_M_PT400_PRESSURE, STRAND2_F_ACTUAL, STRAND2_H_ACTUAL, STRAND2_MLCSSTEELLEVEL,
            MEMO2, CCM_MEMO
        )
        SELECT
            C.HEAT_ID,
            C.CAST_ID,
            'L2',
            C.CCM_DATE,
            C.GROUP1,
            C.CAST_SDL,
            C.HEAT_SDL,
            C.SHIFT,
            C.CAST_SEQ_N,
            C.PENSHUI,
            C.STL_WGT_WAIT,
            C.STL_GRD_CD,
            C.STL_COD,
            C.LADLE_ID,
            C.AUTO_OPEN_FLG,
            C.AVG_CAST_SPEED,
            C.TARGET_ARR_TMP,
            C.LADLE_ARR_TMP,
            C.LADLE_ARR_TIME,
            C.STAR_CAST_TIM,
            C.END_CAST_TIM,
            C.LD_ARR_NET_WGHT,
            C.LD_DEP_NET_WGHT,
            C.CAST_WGT,
            C.HEAT_SLAB_CNT,
            C.TD_NO,
            C.TUNDISH_TEMP_AVG1,
            C.TUNDISH_TEMP_AVG2,
            C.CASTING_POWDER_TYPE,
            C.COVERING_AGENT_TYPE,
            C.MAC_CODE,
            D.SLAB_CNT,
            D.SLAB_WGT,
            C.MEMO,
            B.C,
            B.Mn,
            B.Si,
            B.S,
            B.P,
            B.V,
            B.ALS,
            B.AL,
            B.CA,
            B.TI,
            B.CU,
            B.NI,
            B.B,
            B.N,
            B.CEQ,
            AA.PART_ONE,
            AA.PART_TWO,
            AA.SPEED_ONE,
            AA.SPEED_TWO,
            BB.MOTEMP_ONE,
            BB.MOTEMP_TWO,
            BB.MODIF_ONE,
            BB.MODIF_TWO,
            BB.MOSURE_ONE,
            BB.MOSURE_TWO,
            GG.STRAND_NO1_BHZ,
            GG.STRAND_NO2_BHZ,
            GG.STRAND_NO1_FGJ,
            GG.STRAND_NO2_FGJ
        FROM JGLGMES.SMES_B_CCMRES C,
             (SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID,
                     TREAT_NO,
                     COUNT(1) SLAB_CNT,
                     SUM(SLAB_WEIGHT) SLAB_WGT
              FROM JGLGMES.SMES_B_CUTRES
              GROUP BY SUBSTR(HEAT_ID,0,9), TREAT_NO) D,
             (SELECT *
              FROM JGLGMES.SMES_B_che_steel a
              WHERE a.SEQ_NO IN (SELECT MAX(b.SEQ_NO)
                                 FROM JGLGMES.SMES_B_che_steel b
                                 WHERE a.HEAT_ID = b.HEAT_ID
                                 GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
                AND sampletype LIKE 'C%') B,
             (SELECT HEAT_ID,
                     TREAT_NO,
                     MAX(DECODE(STRAND_NO, '1', MOULD_NO)) PART_ONE,
                     MAX(DECODE(STRAND_NO, '2', MOULD_NO)) PART_TWO,
                     MAX(DECODE(STRAND_NO, '1', SPEEDVALUE)) SPEED_ONE,
                     MAX(DECODE(STRAND_NO, '2', SPEEDVALUE)) SPEED_TWO
              FROM JGLGMES.SMES_B_CCMRES_STREAM
              GROUP BY HEAT_ID, TREAT_NO) AA,
             (SELECT HEAT_ID,
                     TREAT_NO,
                     MAX(DECODE(STRAND_NO, '1', MOULD_ACTVALUE)) MOTEMP_ONE,
                     MAX(DECODE(STRAND_NO, '2', MOULD_ACTVALUE)) MOTEMP_TWO,
                     MAX(DECODE(STRAND_NO, '1', MLD_WAT_T)) MODIF_ONE,
                     MAX(DECODE(STRAND_NO, '2', MLD_WAT_T)) MODIF_TWO,
                     MAX(DECODE(STRAND_NO, '1', MOULDWATERDELTATEMP)) MOSURE_ONE,
                     MAX(DECODE(STRAND_NO, '2', MOULDWATERDELTATEMP)) MOSURE_TWO
              FROM JGLGMES.SMES_B_CCM_PAR
              GROUP BY HEAT_ID, TREAT_NO) BB,
             (SELECT *
              FROM (
                  SELECT *
                  FROM (
                      SELECT t.heat_id,
                             t.treat_no,
                             t.strand_no,
                             DECODE(t.bhz_seq, bb.seq_no, bb.part_id, '') bhz,
                             DECODE(t.fgj_seq, cc.seq_no, cc.part_id, '') fgj
                      FROM JGLGMES.SMES_B_CCMRES_STREAM t,
                           (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%02') bb,
                           (SELECT * FROM JGLGMES.SMES_C_PARTRES WHERE PART_CODE LIKE '%03') cc
                      WHERE t.bhz_seq = bb.seq_no(+)
                        AND t.fgj_seq = cc.seq_no(+)
                  ) AAA
              )
              PIVOT(MAX(BHZ) BHZ, MAX(FGJ) FGJ
                    FOR strand_no IN('1' AS strand_no1,
                                     '2' AS strand_no2))) GG
        WHERE C.HEAT_ID = D.HEAT_ID(+)
          AND C.TREAT_NO = D.TREAT_NO(+)
          AND C.HEAT_ID = B.HEAT_ID(+)
          AND C.HEAT_ID = AA.HEAT_ID(+)
          AND C.TREAT_NO = AA.TREAT_NO(+)
          AND C.HEAT_ID = BB.HEAT_ID(+)
          AND C.TREAT_NO = BB.TREAT_NO(+)
          AND C.HEAT_ID = GG.HEAT_ID(+)
          AND C.TREAT_NO = GG.TREAT_NO(+)
          AND C.WORK_SHOP = 'L2'
          AND TO_CHAR(C.STAR_CAST_TIM, 'YYYY-MM-DD') = TO_CHAR(#{date}, 'YYYY-MM-DD')
          AND C.MAC_CODE IN ('2D312','2D313')
          AND C.HEAT_ID NOT LIKE 'L5%'
        ORDER BY C.STAR_CAST_TIM ASC
    </insert>

    <!-- 根据日期删除数据 -->
    <delete id="deleteByDate" parameterType="java.util.Date">
        DELETE FROM JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER
        WHERE TRUNC(P_DATE) = TRUNC(#{date})
    </delete>

    <!-- 清空表数据 -->
    <delete id="truncate">
        TRUNCATE TABLE JGLGMES.CARBON_LGMS_L2_CCM_BP_REPORTER
    </delete>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jggroup.fakereports.business.mapper.CarbonLgmsBofReporterMapper">

    <!-- 批量插入转炉报表数据 -->
    <insert id="insertAll" parameterType="java.util.List">
        INSERT INTO JGLGMES.CARBON_LGMS_BOF_REPORTER (
            P_DATE, HEAT_ID, WORK_SHOP, S_LD_ID, MAIN_QX,
            DEP_TIME, SHAKER, TRANSFER_ID, AR_O, SHIFT, GROUP1, MAC_CODE,
            FG_NORMAL, LAN_POS_MIN, FURNACE_TIMS, STL_PORT_TIMS, GUN_ONE_TIMS, GUN_TWO_TIMS,
            PLAN_STL_GRD, STL_GRD, VENT_STATUS, MEMO2, MEMO3,
            TAP_START, TAP_END, TAP_TIME, PLAN_CCM_NO,
            OX_START, OX_END, OUT_TPM, BLOW_TIME, BLOW_NUMBER,
            IRON_WGT, SCRAP_WGT, TOTAL_SCRAP, IRONSCWGT,
            IRON_C, SI, S, MN, P, IRON_TI, IRON_TMP, IRON_TIME,
            OX_TIME, BO_CSM,
            FINISH_C, FINISH_MN, FINISH_P, FINISH_S, FINISH_SI, FINISH_TMP,
            TSC_TEMP, TSC_C, TSO_TEMP, TSO_C, TSO_O,
            SLAG_SPLASHING_TIME, NO_CSM, DZINFOMATION,
            AR_TMP_BEFORE, AR_TMP_AFTER, AR_TIME, AR_TMP,
            BOF_C, BOF_MN, BOF_SI, BOF_S, BOF_P, BOF_V, BOF_AL, BOF_N, BOF_CA,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA,
            LIMESTONE, DOLOMITE, LADLE_COVER, SLAG_MODIFIER, LIME_BLOCK, LIGHT_DOLOMITE,
            COARSE_PARTICLE, IRON_SCALE, FLUX, SI_MN_ALLOY, SI_FE_ALLOY, V_N_ALLOY,
            SMALL_AL, COMPLEX_ALLOY, CARBURIZER, CA_WIRE, COLD_MATERIAL, SINTER_ORE,
            BF_RETURN, TI_FE_ALLOY, O2_TUBE, TI_FE_WIRE, COMPREHENSIVE_SCRAP,
            SELF_CYCLE, KILN_SLAG, MAG_POWDER, STONE, SELF_RETURN_COKE,
            STL_WGT, REMARK, ZZ_SCRAP_WGT, ZZ_PI_WGT, SLAB_WEIGHT
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.pDate}, #{item.heatId}, #{item.workShop}, #{item.sLdId}, #{item.mainQx},
                #{item.depTime}, #{item.shaker}, #{item.transferId}, #{item.arO}, #{item.shift}, #{item.group1}, #{item.macCode},
                #{item.fgNormal}, #{item.lanPosMin}, #{item.furnaceTims}, #{item.stlPortTims}, #{item.gunOneTims}, #{item.gunTwoTims},
                #{item.planStlGrd}, #{item.stlGrd}, #{item.ventStatus}, #{item.memo2}, #{item.memo3},
                #{item.tapStart}, #{item.tapEnd}, #{item.tapTime}, #{item.planCcmNo},
                #{item.oxStart}, #{item.oxEnd}, #{item.outTpm}, #{item.blowTime}, #{item.blowNumber},
                #{item.ironWgt}, #{item.scrapWgt}, #{item.totalScrap}, #{item.ironscwgt},
                #{item.ironC}, #{item.si}, #{item.s}, #{item.mn}, #{item.p}, #{item.ironTi}, #{item.ironTmp}, #{item.ironTime},
                #{item.oxTime}, #{item.boCsm},
                #{item.finishC}, #{item.finishMn}, #{item.finishP}, #{item.finishS}, #{item.finishSi}, #{item.finishTmp},
                #{item.tscTemp}, #{item.tscC}, #{item.tsoTemp}, #{item.tsoC}, #{item.tsoO},
                #{item.slagSplashingTime}, #{item.noCsm}, #{item.dzinfomation},
                #{item.arTmpBefore}, #{item.arTmpAfter}, #{item.arTime}, #{item.arTmp},
                #{item.bofC}, #{item.bofMn}, #{item.bofSi}, #{item.bofS}, #{item.bofP}, #{item.bofV}, #{item.bofAl}, #{item.bofN}, #{item.bofCa},
                #{item.ccmC}, #{item.ccmMn}, #{item.ccmSi}, #{item.ccmS}, #{item.ccmP}, #{item.ccmV}, #{item.ccmAl}, #{item.ccmN}, #{item.ccmCa},
                #{item.limestone}, #{item.dolomite}, #{item.ladleCover}, #{item.slagModifier}, #{item.limeBlock}, #{item.lightDolomite},
                #{item.coarseParticle}, #{item.ironScale}, #{item.flux}, #{item.siMnAlloy}, #{item.siFeAlloy}, #{item.vNAlloy},
                #{item.smallAl}, #{item.complexAlloy}, #{item.carburizer}, #{item.caWire}, #{item.coldMaterial}, #{item.sinterOre},
                #{item.bfReturn}, #{item.tiFeAlloy}, #{item.o2Tube}, #{item.tiFeWire}, #{item.comprehensiveScrap},
                #{item.selfCycle}, #{item.kilnSlag}, #{item.magPowder}, #{item.stone}, #{item.selfReturnCoke},
                #{item.stlWgt}, #{item.remark}, #{item.zzScrapWgt}, #{item.zzPiWgt}, #{item.slabWeight}
            )
        </foreach>
    </insert>

    <!-- 从源表查询L1数据 -->
    <select id="selectL1FromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsBofReporter">
        SELECT
            H.STAR_CAST_TIM as pDate,
            A.HEAT_ID as heatId,
            'L1' as workShop,
            A.S_LD_ID as sLdId,
            DECODE(P.LF_NO, NULL, '直上', '精炼') as mainQx,
            A.DEP_TIME as depTime,
            A.SHAKER as shaker,
            A.TRANSFER_ID as transferId,
            A.AR_O as arO,
            A.SHIFT as shift,
            A.GROUP1 as group1,
            A.MAC_CODE as macCode,
            A.FG_NORMAL as fgNormal,
            A.LAN_POS_MIN as lanPosMin,
            A.FURNACE_TIMS as furnaceTims,
            A.STL_PORT_TIMS as stlPortTims,
            A.GUN_ONE_TIMS as gunOneTims,
            A.GUN_TWO_TIMS as gunTwoTims,
            A.PLAN_STL_GRD as planStlGrd,
            A.STL_GRD as stlGrd,
            A.VENT_STATUS as ventStatus,
            A.MEMO2 as memo2,
            A.MEMO3 as memo3,
            A.TAP_START as tapStart,
            A.TAP_END as tapEnd,
            ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60) as tapTime,
            A.PLAN_CCM_NO as planCcmNo,
            A.OX_START as oxStart,
            A.OX_END as oxEnd,
            A.OUT_TPM as outTpm,
            A.BLOW_TIME as blowTime,
            A.BLOW_NUMBER as blowNumber,
            A.IRON_WGT as ironWgt,
            N.SCRAP_WGT as scrapWgt,
            A.TOTAL_SCRAP as totalScrap,
            (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)) as ironscwgt,
            B.C as ironC,
            B.SI as si,
            B.S as s,
            B.MN as mn,
            B.P as p,
            B.TI as ironTi,
            A.IRON_TMP as ironTmp,
            A.IRON_TIME as ironTime,
            A.OX_TIME as oxTime,
            A.BO_CSM as boCsm,
            J.C as finishC,
            J.MN as finishMn,
            J.P as finishP,
            J.S as finishS,
            J.SI as finishSi,
            (A.TSO_TEMP + A.BLOW_TIME) as finishTmp,
            A.TSC_TEMP as tscTemp,
            A.TSC_C as tscC,
            A.TSO_TEMP as tsoTemp,
            A.TSO_C as tsoC,
            A.TSO_O as tsoO,
            ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60) as slagSplashingTime,
            A.NO_CSM as noCsm,
            A.JB_INFO as dzinfomation,
            '' as arTmpBefore,
            '' as arTmpAfter,
            AR_TIME as arTime,
            A.AR_TMP as arTmp,
            D.C as bofC,
            D.Mn as bofMn,
            D.Si as bofSi,
            D.S as bofS,
            D.P as bofP,
            D.V as bofV,
            D.AL as bofAl,
            D.N as bofN,
            D.Ca as bofCa,
            C.C as ccmC,
            C.Mn as ccmMn,
            C.Si as ccmSi,
            C.S as ccmS,
            C.P as ccmP,
            C.V as ccmV,
            C.AL as ccmAl,
            C.N as ccmN,
            C.Ca as ccmCa,
            G.石灰石 as limestone,
            G.白云石 as dolomite,
            G.钢包覆盖剂 as ladleCover,
            G.钢渣改质剂 as slagModifier,
            G.石灰块 as limeBlock,
            G.轻烧白云石 as lightDolomite,
            G.粗颗粒 as coarseParticle,
            G.氧化铁皮 as ironScale,
            G.高效复合助熔剂 as flux,
            G.硅锰合金 as siMnAlloy,
            G.硅铁 as siFeAlloy,
            G.钒氮合金 as vNAlloy,
            G.小铝锭 as smallAl,
            G.复析合金 as complexAlloy,
            G.增碳剂 as carburizer,
            G.金属钙线 as caWire,
            G.冷料 as coldMaterial,
            G.烧结矿 as sinterOre,
            G.高炉返矿 as bfReturn,
            G.钛铁 as tiFeAlloy,
            G.定氧管 as o2Tube,
            G.钛铁包芯线 as tiFeWire,
            DECODE(round((G.综合废钢 + G.综合废钢2)/1000,3),0,M.ZZ_SCRAP_WGT,round((G.综合废钢 + G.综合废钢2)/1000,3)) as comprehensiveScrap,
            round(G.自循环/1000,3) as selfCycle,
            G.窑渣 as kilnSlag,
            G.磁选精粉 as magPowder,
            G.石子 as stone,
            G.自返焦粉 as selfReturnCoke,
            A.STL_WGT as stlWgt,
            A.REMARK as remark,
            M.ZZ_SCRAP_WGT as zzScrapWgt,
            M.ZZ_PI_WGT as zzPiWgt,
            F.SLAB_WEIGHT as slabWeight
        FROM JGLGMES.SMES_B_BOF_RES A
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240101') B ON A.HEAT_ID = B.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240101') N ON A.IRON_NO = N.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
        LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID, 0, 9)) F ON A.HEAT_ID = F.HEAT_ID
        LEFT JOIN (SELECT T.HEAT_ID,
                          SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT,'10501012', T.MAT_WGT, 0)) 石灰石,
                          SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT,'10501029', T.MAT_WGT, 0)) 白云石,
                          SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, 0)) 钢包覆盖剂,
                          SUM(DECODE(T.MAT_CODE, '10502008', T.MAT_WGT, 0)) 钢渣改质剂,
                          SUM(DECODE(T.MAT_CODE, '10705001', T.MAT_WGT,'10501024', T.MAT_WGT, 0)) 石灰块,
                          SUM(DECODE(T.MAT_CODE, '10501002', T.MAT_WGT,'10705003', T.MAT_WGT, 0)) 轻烧白云石,
                          SUM(DECODE(T.MAT_CODE, '10604002', T.MAT_WGT, 0)) 粗颗粒,
                          SUM(DECODE(T.MAT_CODE, '10606001', T.MAT_WGT, 0)) 氧化铁皮,
                          SUM(DECODE(T.MAT_CODE, '10502003', T.MAT_WGT, 0)) 高效复合助熔剂,
                          SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰合金,
                          SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                          SUM(DECODE(T.MAT_CODE, '10201003', T.MAT_WGT, 0)) 钒氮合金,
                          SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 小铝锭,
                          SUM(DECODE(T.MAT_CODE, '10201009', T.MAT_WGT, 0)) 复析合金,
                          SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 增碳剂,
                          SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 金属钙线,
                          SUM(DECODE(T.MAT_CODE, '10604017', T.MAT_WGT, 0)) 冷料,
                          SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 烧结矿,
                          SUM(DECODE(T.MAT_CODE, '10603001', T.MAT_WGT, 0)) 高炉返矿,
                          SUM(DECODE(T.MAT_CODE, '10201007', T.MAT_WGT, 0)) 钛铁,
                          SUM(DECODE(T.MAT_CODE, '10504022', T.MAT_WGT, 0)) 定氧管,
                          SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, 0)) 钛铁包芯线,
                          SUM(DECODE(T.MAT_CODE, '10501035', T.MAT_WGT,'10501036', T.MAT_WGT, 0)) 石子,
                          SUM(DECODE(T.MAT_CODE, '10401014', T.MAT_WGT, 0)) 综合废钢,
                          SUM(DECODE(T.MAT_CODE, '10401015', T.MAT_WGT, 0)) 综合废钢2,
                          SUM(DECODE(T.MAT_CODE, '1001002', T.MAT_WGT,'1001001', T.MAT_WGT, 0)) 自循环,
                          SUM(DECODE(T.MAT_CODE, '10701002', T.MAT_WGT, 0)) 窑渣,
                          SUM(DECODE(T.MAT_CODE, '10607001', T.MAT_WGT, 0)) 磁选精粉,
                          SUM(DECODE(T.MAT_CODE, '10603008', T.MAT_WGT, 0)) 自返焦粉
                   FROM JGLGMES.SMES_B_MAT_USE t WHERE T.wp_code = '1C01' GROUP BY T.HEAT_ID) G ON A.HEAT_ID = G.HEAT_ID
        LEFT JOIN (SELECT heat_id, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM JGLGMES.SMES_B_CCMRES
                   where MAC_CODE in ('1D311','1D313','1D314')
                   GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
        LEFT JOIN (SELECT SEQ_NO, ZZ_SCRAP_WGT, ZZ_PI_WGT FROM JGLGMES.SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
        LEFT JOIN JGLGMES.SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
        WHERE A.WORK_SHOP = 'L1'
          and A.MAC_CODE != '1C015'
          AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
    </select>

    <!-- 从源表查询L2数据 -->
    <select id="selectL2FromSource" parameterType="java.util.Date" resultType="cn.jggroup.fakereports.business.entity.steel.CarbonLgmsBofReporter">
        SELECT
            H.STAR_CAST_TIM as pDate,
            A.HEAT_ID as heatId,
            'L2' as workShop,
            A.I_LD_ID as iLdId,
            A.S_LD_ID as sLdId,
            A.LD_TYPE as ldType,
            DECODE(P.LF_NO, NULL, '直上', '精炼') as mainQx,
            A.LAN_POS_MAX as lanPosMax,
            A.SCRAP_NO as scrapNo,
            A.DEP_TIME as depTime,
            A.SHAKER as shaker,
            A.TRANSFER_ID as transferId,
            A.AR_O as arO,
            A.SHIFT as shift,
            A.GROUP1 as group1,
            A.MAC_CODE as macCode,
            A.FG_NORMAL as fgNormal,
            A.LAN_POS_MIN as lanPosMin,
            A.FURNACE_TIMS as furnaceTims,
            A.STL_PORT_TIMS as stlPortTims,
            A.GUN_ONE_TIMS as gunOneTims,
            A.GUN_TWO_TIMS as gunTwoTims,
            A.PLAN_STL_GRD as planStlGrd,
            A.STL_GRD as stlGrd,
            A.STL_ROLL_TMP as stlRollTmp,
            H.MAT_QUL_CD as matQulCd,
            A.VENT_STATUS as ventStatus,
            N.IN_SCRAP_WGT as inScrapWgt,
            N.IRON_GROSS_WGT as ironGrossWgt,
            N.IRON_NET_WGT as ironNetWgt,
            N.SCRAP_WGT as scrapWgt,
            A.TOTAL_SCRAP as totalScrap,
            (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)) as ironscwgt,
            B.C as ironC,
            B.SI as si,
            B.S as s,
            B.MN as mn,
            B.P as p,
            B.TI as ironTi,
            A.IRON_TMP as ironTmp,
            A.IRON_TIME as ironTime,
            A.OX_START as oxStart,
            A.OX_TIME as oxTime,
            A.BO_CSM as boCsm,
            J.C as finishC,
            J.MN as finishMn,
            J.P as finishP,
            J.S as finishS,
            (A.TSO_TEMP+A.BLOW_TIME) as finishTmp,
            A.TSC_TEMP as tscTemp,
            A.TSC_C as tscC,
            A.TSO_TEMP as tsoTemp,
            A.TSO_C as tsoC,
            A.TSO_O as tsoO,
            A.BLOW_TIME as blowTime,
            A.BLOW_NUMBER as blowNumber,
            ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60) as tapTime,
            A.JB_INFO as dzinfomation,
            ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60) as slagSplashingTime,
            A.NO_CSM as noCsm,
            '' as arTmpBefore,
            '' as arTmpAfter,
            AR_TIME as arTime,
            A.AR_TMP as arTmp,
            D.C as bofC,
            D.Mn as bofMn,
            D.Si as bofSi,
            D.S as bofS,
            D.P as bofP,
            D.V as bofV,
            D.AL as bofAl,
            D.N as bofN,
            D.Ca as bofCa,
            C.C as ccmC,
            C.Mn as ccmMn,
            C.Si as ccmSi,
            C.S as ccmS,
            C.P as ccmP,
            C.V as ccmV,
            C.AL as ccmAl,
            C.N as ccmN,
            C.Ca as ccmCa,
            E.C as mC,
            E.Mn as mMn,
            E.Si as mSi,
            E.S as mS,
            E.P as mP,
            E.AL as mAl,
            A.STL_WGT as stlWgt,
            A.REMARK as remark,
            M.ZZ_SCRAP_WGT as zzScrapWgt,
            M.ZZ_PI_WGT as zzPiWgt,
            F.SLAB_WEIGHT as slabWeight,
            Q.RE_TIME as reTime,
            Q.RE_NUM_COM as reNumCom
        FROM JGLGMES.SMES_B_BOF_RES A
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240601') B ON A.IRON_NO = B.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240601') N ON A.IRON_NO = N.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'M%') E ON A.HEAT_ID = E.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
        LEFT JOIN (SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID,0,9)) F ON A.HEAT_ID = F.HEAT_ID
        LEFT JOIN (SELECT heat_id, MAX(MAT_QUL_CD) MAT_QUL_CD, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM JGLGMES.SMES_B_CCMRES
                   WHERE MAC_CODE IN ('2D312','2D313')
                   GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
        LEFT JOIN (SELECT SEQ_NO, NVL(RECYCLED_STEEL,ZZ_SCRAP_WGT) ZZ_SCRAP_WGT, NVL(SINCE_CYCLE,ZZ_PI_WGT) ZZ_PI_WGT FROM JGLGMES.SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
        LEFT JOIN JGLGMES.SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
        LEFT JOIN (SELECT HEAT_ID,MAX(RE_TIME) AS RE_TIME,MAX(RE_NUM_COM) AS RE_NUM_COM FROM JGLGMES.SMES_B_ENER_USEC GROUP BY HEAT_ID) Q ON A.HEAT_ID = Q.HEAT_ID
        WHERE A.WORK_SHOP = 'L2'
          AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
    </select>

    <!-- 根据日期从源表查询数据并插入到报表表 -->
    <insert id="insertFromSource" parameterType="java.util.Date">
        <!-- 一钢数据插入 -->
        INSERT INTO JGLGMES.CARBON_LGMS_BOF_REPORTER (
            P_DATE, HEAT_ID, WORK_SHOP, S_LD_ID, MAIN_QX,
            DEP_TIME, SHAKER, TRANSFER_ID, AR_O, SHIFT, GROUP1, MAC_CODE,
            FG_NORMAL, LAN_POS_MIN, FURNACE_TIMS, STL_PORT_TIMS, GUN_ONE_TIMS, GUN_TWO_TIMS,
            PLAN_STL_GRD, STL_GRD, VENT_STATUS, MEMO2, MEMO3,
            TAP_START, TAP_END, TAP_TIME, PLAN_CCM_NO,
            OX_START, OX_END, OUT_TPM, BLOW_TIME, BLOW_NUMBER,
            IRON_WGT, SCRAP_WGT, TOTAL_SCRAP, IRONSCWGT,
            IRON_C, SI, S, MN, P, IRON_TI, IRON_TMP, IRON_TIME,
            OX_TIME, BO_CSM,
            FINISH_C, FINISH_MN, FINISH_P, FINISH_S, FINISH_SI, FINISH_TMP,
            TSC_TEMP, TSC_C, TSO_TEMP, TSO_C, TSO_O,
            SLAG_SPLASHING_TIME, NO_CSM, DZINFOMATION,
            AR_TMP_BEFORE, AR_TMP_AFTER, AR_TIME, AR_TMP,
            BOF_C, BOF_MN, BOF_SI, BOF_S, BOF_P, BOF_V, BOF_AL, BOF_N, BOF_CA,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA,
            LIMESTONE, DOLOMITE, LADLE_COVER, SLAG_MODIFIER, LIME_BLOCK, LIGHT_DOLOMITE,
            COARSE_PARTICLE, IRON_SCALE, FLUX, SI_MN_ALLOY, SI_FE_ALLOY, V_N_ALLOY,
            SMALL_AL, COMPLEX_ALLOY, CARBURIZER, CA_WIRE, COLD_MATERIAL, SINTER_ORE,
            BF_RETURN, TI_FE_ALLOY, O2_TUBE, TI_FE_WIRE, COMPREHENSIVE_SCRAP,
            SELF_CYCLE, KILN_SLAG, MAG_POWDER, STONE, SELF_RETURN_COKE,
            STL_WGT, REMARK, ZZ_SCRAP_WGT, ZZ_PI_WGT, SLAB_WEIGHT
        )
        SELECT
            H.STAR_CAST_TIM,
            A.HEAT_ID,
            'L1',
            A.S_LD_ID,
            DECODE(P.LF_NO, NULL, '直上', '精炼'),
            A.DEP_TIME,
            A.SHAKER,
            A.TRANSFER_ID,
            A.AR_O,
            A.SHIFT,
            A.GROUP1,
            A.MAC_CODE,
            A.FG_NORMAL,
            A.LAN_POS_MIN,
            A.FURNACE_TIMS,
            A.STL_PORT_TIMS,
            A.GUN_ONE_TIMS,
            A.GUN_TWO_TIMS,
            A.PLAN_STL_GRD,
            A.STL_GRD,
            A.VENT_STATUS,
            A.MEMO2,
            A.MEMO3,
            A.TAP_START,
            A.TAP_END,
            ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60),
            A.PLAN_CCM_NO,
            A.OX_START,
            A.OX_END,
            A.OUT_TPM,
            A.BLOW_TIME,
            A.BLOW_NUMBER,
            A.IRON_WGT,
            N.SCRAP_WGT,
            A.TOTAL_SCRAP,
            (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)),
            B.C,
            B.SI,
            B.S,
            B.MN,
            B.P,
            B.TI,
            A.IRON_TMP,
            A.IRON_TIME,
            A.OX_TIME,
            A.BO_CSM,
            J.C,
            J.MN,
            J.P,
            J.S,
            J.SI,
            (A.TSO_TEMP + A.BLOW_TIME),
            A.TSC_TEMP,
            A.TSC_C,
            A.TSO_TEMP,
            A.TSO_C,
            A.TSO_O,
            ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60),
            A.NO_CSM,
            A.JB_INFO,
            '',
            '',
            AR_TIME,
            A.AR_TMP,
            D.C,
            D.Mn,
            D.Si,
            D.S,
            D.P,
            D.V,
            D.AL,
            D.N,
            D.Ca,
            C.C,
            C.Mn,
            C.Si,
            C.S,
            C.P,
            C.V,
            C.AL,
            C.N,
            C.Ca,
            G.石灰石,
            G.白云石,
            G.钢包覆盖剂,
            G.钢渣改质剂,
            G.石灰块,
            G.轻烧白云石,
            G.粗颗粒,
            G.氧化铁皮,
            G.高效复合助熔剂,
            G.硅锰合金,
            G.硅铁,
            G.钒氮合金,
            G.小铝锭,
            G.复析合金,
            G.增碳剂,
            G.金属钙线,
            G.冷料,
            G.烧结矿,
            G.高炉返矿,
            G.钛铁,
            G.定氧管,
            G.钛铁包芯线,
            DECODE(round((G.综合废钢 + G.综合废钢2)/1000,3),0,M.ZZ_SCRAP_WGT,round((G.综合废钢 + G.综合废钢2)/1000,3)),
            round(G.自循环/1000,3),
            G.窑渣,
            G.磁选精粉,
            G.石子,
            G.自返焦粉,
            A.STL_WGT,
            A.REMARK,
            M.ZZ_SCRAP_WGT,
            M.ZZ_PI_WGT,
            F.SLAB_WEIGHT
        FROM JGLGMES.SMES_B_BOF_RES A
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240101') B ON A.HEAT_ID = B.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240101') N ON A.IRON_NO = N.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
        LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID, 0, 9)) F ON A.HEAT_ID = F.HEAT_ID
        LEFT JOIN (SELECT T.HEAT_ID,
                          SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT,'10501012', T.MAT_WGT, 0)) 石灰石,
                          SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT,'10501029', T.MAT_WGT, 0)) 白云石,
                          SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, 0)) 钢包覆盖剂,
                          SUM(DECODE(T.MAT_CODE, '10502008', T.MAT_WGT, 0)) 钢渣改质剂,
                          SUM(DECODE(T.MAT_CODE, '10705001', T.MAT_WGT,'10501024', T.MAT_WGT, 0)) 石灰块,
                          SUM(DECODE(T.MAT_CODE, '10501002', T.MAT_WGT,'10705003', T.MAT_WGT, 0)) 轻烧白云石,
                          SUM(DECODE(T.MAT_CODE, '10604002', T.MAT_WGT, 0)) 粗颗粒,
                          SUM(DECODE(T.MAT_CODE, '10606001', T.MAT_WGT, 0)) 氧化铁皮,
                          SUM(DECODE(T.MAT_CODE, '10502003', T.MAT_WGT, 0)) 高效复合助熔剂,
                          SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰合金,
                          SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                          SUM(DECODE(T.MAT_CODE, '10201003', T.MAT_WGT, 0)) 钒氮合金,
                          SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 小铝锭,
                          SUM(DECODE(T.MAT_CODE, '10201009', T.MAT_WGT, 0)) 复析合金,
                          SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 增碳剂,
                          SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 金属钙线,
                          SUM(DECODE(T.MAT_CODE, '10604017', T.MAT_WGT, 0)) 冷料,
                          SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 烧结矿,
                          SUM(DECODE(T.MAT_CODE, '10603001', T.MAT_WGT, 0)) 高炉返矿,
                          SUM(DECODE(T.MAT_CODE, '10201007', T.MAT_WGT, 0)) 钛铁,
                          SUM(DECODE(T.MAT_CODE, '10504022', T.MAT_WGT, 0)) 定氧管,
                          SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, 0)) 钛铁包芯线,
                          SUM(DECODE(T.MAT_CODE, '10501035', T.MAT_WGT,'10501036', T.MAT_WGT, 0)) 石子,
                          SUM(DECODE(T.MAT_CODE, '10401014', T.MAT_WGT, 0)) 综合废钢,
                          SUM(DECODE(T.MAT_CODE, '10401015', T.MAT_WGT, 0)) 综合废钢2,
                          SUM(DECODE(T.MAT_CODE, '1001002', T.MAT_WGT,'1001001', T.MAT_WGT, 0)) 自循环,
                          SUM(DECODE(T.MAT_CODE, '10701002', T.MAT_WGT, 0)) 窑渣,
                          SUM(DECODE(T.MAT_CODE, '10607001', T.MAT_WGT, 0)) 磁选精粉,
                          SUM(DECODE(T.MAT_CODE, '10603008', T.MAT_WGT, 0)) 自返焦粉
                   FROM JGLGMES.SMES_B_MAT_USE t WHERE T.wp_code = '1C01' GROUP BY T.HEAT_ID) G ON A.HEAT_ID = G.HEAT_ID
        LEFT JOIN (SELECT heat_id, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM JGLGMES.SMES_B_CCMRES
                   where MAC_CODE in ('1D311','1D313','1D314')
                   GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
        LEFT JOIN (SELECT SEQ_NO, ZZ_SCRAP_WGT, ZZ_PI_WGT FROM JGLGMES.SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
        LEFT JOIN JGLGMES.SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
        WHERE A.WORK_SHOP = 'L1'
          and A.MAC_CODE != '1C015'
          AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
    </insert>

    <!-- 根据日期删除数据 -->
    <!-- 一钢数据插入 -->
    <insert id="insertFromSourceByL1" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_BOF_REPORTER (
            P_DATE, HEAT_ID, WORK_SHOP, S_LD_ID, MAIN_QX,
            DEP_TIME, SHAKER, TRANSFER_ID, AR_O, SHIFT, GROUP1, MAC_CODE,
            FG_NORMAL, LAN_POS_MIN, FURNACE_TIMS, STL_PORT_TIMS, GUN_ONE_TIMS, GUN_TWO_TIMS,
            PLAN_STL_GRD, STL_GRD, VENT_STATUS, MEMO2, MEMO3,
            TAP_START, TAP_END, TAP_TIME, PLAN_CCM_NO,
            OX_START, OX_END, OUT_TPM, BLOW_TIME, BLOW_NUMBER,
            IRON_WGT, SCRAP_WGT, TOTAL_SCRAP, IRONSCWGT,
            IRON_C, SI, S, MN, P, IRON_TI, IRON_TMP, IRON_TIME,
            OX_TIME, BO_CSM,
            FINISH_C, FINISH_MN, FINISH_P, FINISH_S, FINISH_SI, FINISH_TMP,
            TSC_TEMP, TSC_C, TSO_TEMP, TSO_C, TSO_O,
            SLAG_SPLASHING_TIME, NO_CSM, DZINFOMATION,
            AR_TMP_BEFORE, AR_TMP_AFTER, AR_TIME, AR_TMP,
            BOF_C, BOF_MN, BOF_SI, BOF_S, BOF_P, BOF_V, BOF_AL, BOF_N, BOF_CA,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA,
            LIMESTONE, DOLOMITE, LADLE_COVER, SLAG_MODIFIER, LIME_BLOCK, LIGHT_DOLOMITE,
            COARSE_PARTICLE, IRON_SCALE, FLUX, SI_MN_ALLOY, SI_FE_ALLOY, V_N_ALLOY,
            SMALL_AL, COMPLEX_ALLOY, CARBURIZER, CA_WIRE, COLD_MATERIAL, SINTER_ORE,
            BF_RETURN, TI_FE_ALLOY, O2_TUBE, TI_FE_WIRE, COMPREHENSIVE_SCRAP,
            SELF_CYCLE, KILN_SLAG, MAG_POWDER, STONE, SELF_RETURN_COKE,
            STL_WGT, REMARK, ZZ_SCRAP_WGT, ZZ_PI_WGT, SLAB_WEIGHT
        )
        SELECT
            H.STAR_CAST_TIM,
            A.HEAT_ID,
            'L1',
            A.S_LD_ID,
            DECODE(P.LF_NO, NULL, '直上', '精炼'),
            A.DEP_TIME, A.SHAKER, A.TRANSFER_ID, A.AR_O, A.SHIFT, A.GROUP1, A.MAC_CODE,
            A.FG_NORMAL, A.LAN_POS_MIN, A.FURNACE_TIMS, A.STL_PORT_TIMS, A.GUN_ONE_TIMS, A.GUN_TWO_TIMS,
            A.PLAN_STL_GRD, A.STL_GRD, A.VENT_STATUS, A.MEMO2, A.MEMO3,
            A.TAP_START, A.TAP_END, ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60), A.PLAN_CCM_NO,
            A.OX_START, A.OX_END, A.OUT_TPM, A.BLOW_TIME, A.BLOW_NUMBER,
            A.IRON_WGT, N.SCRAP_WGT, A.TOTAL_SCRAP, (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)),
            B.C, B.SI, B.S, B.MN, B.P, B.TI, A.IRON_TMP, A.IRON_TIME,
            A.OX_TIME, A.BO_CSM,
            J.C, J.MN, J.P, J.S, J.SI, (A.TSO_TEMP + A.BLOW_TIME),
            A.TSC_TEMP, A.TSC_C, A.TSO_TEMP, A.TSO_C, A.TSO_O,
            ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60), A.NO_CSM, A.JB_INFO,
            '', '', AR_TIME, A.AR_TMP,
            D.C, D.Mn, D.Si, D.S, D.P, D.V, D.AL, D.N, D.Ca,
            C.C, C.Mn, C.Si, C.S, C.P, C.V, C.AL, C.N, C.Ca,
            G.石灰石, G.白云石, G.钢包覆盖剂, G.钢渣改质剂, G.石灰块, G.轻烧白云石,
            G.粗颗粒, G.氧化铁皮, G.高效复合助熔剂, G.硅锰合金, G.硅铁, G.钒氮合金,
            G.小铝锭, G.复析合金, G.增碳剂, G.金属钙线, G.冷料, G.烧结矿,
            G.高炉返矿, G.钛铁, G.定氧管, G.钛铁包芯线, DECODE(round((G.综合废钢 + G.综合废钢2)/1000,3),0,M.ZZ_SCRAP_WGT,round((G.综合废钢 + G.综合废钢2)/1000,3)),
            round(G.自循环/1000,3), G.窑渣, G.磁选精粉, G.石子, G.自返焦粉,
            A.STL_WGT, A.REMARK, M.ZZ_SCRAP_WGT, M.ZZ_PI_WGT, F.SLAB_WEIGHT
        FROM JGLGMES.SMES_B_BOF_RES A
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240101') B ON A.HEAT_ID = B.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240101') N ON A.IRON_NO = N.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
        LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID, 0, 9)) F ON A.HEAT_ID = F.HEAT_ID
        LEFT JOIN (SELECT T.HEAT_ID,
                          SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT,'10501012', T.MAT_WGT, 0)) 石灰石,
                          SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT,'10501029', T.MAT_WGT, 0)) 白云石,
                          SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, 0)) 钢包覆盖剂,
                          SUM(DECODE(T.MAT_CODE, '10502008', T.MAT_WGT, 0)) 钢渣改质剂,
                          SUM(DECODE(T.MAT_CODE, '10705001', T.MAT_WGT,'10501024', T.MAT_WGT, 0)) 石灰块,
                          SUM(DECODE(T.MAT_CODE, '10501002', T.MAT_WGT,'10705003', T.MAT_WGT, 0)) 轻烧白云石,
                          SUM(DECODE(T.MAT_CODE, '10604002', T.MAT_WGT, 0)) 粗颗粒,
                          SUM(DECODE(T.MAT_CODE, '10606001', T.MAT_WGT, 0)) 氧化铁皮,
                          SUM(DECODE(T.MAT_CODE, '10502003', T.MAT_WGT, 0)) 高效复合助熔剂,
                          SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰合金,
                          SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                          SUM(DECODE(T.MAT_CODE, '10201003', T.MAT_WGT, 0)) 钒氮合金,
                          SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 小铝锭,
                          SUM(DECODE(T.MAT_CODE, '10201009', T.MAT_WGT, 0)) 复析合金,
                          SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 增碳剂,
                          SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 金属钙线,
                          SUM(DECODE(T.MAT_CODE, '10604017', T.MAT_WGT, 0)) 冷料,
                          SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 烧结矿,
                          SUM(DECODE(T.MAT_CODE, '10603001', T.MAT_WGT, 0)) 高炉返矿,
                          SUM(DECODE(T.MAT_CODE, '10201007', T.MAT_WGT, 0)) 钛铁,
                          SUM(DECODE(T.MAT_CODE, '10504022', T.MAT_WGT, 0)) 定氧管,
                          SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, 0)) 钛铁包芯线,
                          SUM(DECODE(T.MAT_CODE, '10501035', T.MAT_WGT,'10501036', T.MAT_WGT, 0)) 石子,
                          SUM(DECODE(T.MAT_CODE, '10401014', T.MAT_WGT, 0)) 综合废钢,
                          SUM(DECODE(T.MAT_CODE, '10401015', T.MAT_WGT, 0)) 综合废钢2,
                          SUM(DECODE(T.MAT_CODE, '1001002', T.MAT_WGT,'1001001', T.MAT_WGT, 0)) 自循环,
                          SUM(DECODE(T.MAT_CODE, '10701002', T.MAT_WGT, 0)) 窑渣,
                          SUM(DECODE(T.MAT_CODE, '10607001', T.MAT_WGT, 0)) 磁选精粉,
                          SUM(DECODE(T.MAT_CODE, '10603008', T.MAT_WGT, 0)) 自返焦粉
                   FROM JGLGMES.SMES_B_MAT_USE t WHERE T.wp_code = '1C01' GROUP BY T.HEAT_ID) G ON A.HEAT_ID = G.HEAT_ID
        LEFT JOIN (SELECT heat_id, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM JGLGMES.SMES_B_CCMRES
                   where MAC_CODE in ('1D311','1D313','1D314')
                   GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
        LEFT JOIN (SELECT SEQ_NO, ZZ_SCRAP_WGT, ZZ_PI_WGT FROM JGLGMES.SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
        LEFT JOIN JGLGMES.SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
        WHERE A.WORK_SHOP = 'L1'
          and A.MAC_CODE != '1C015'
          AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
    </insert>

    <!-- 二钢数据插入 -->
    <insert id="insertFromSourceByL2" parameterType="java.util.Date">
        INSERT INTO JGLGMES.CARBON_LGMS_BOF_REPORTER (
            P_DATE, HEAT_ID, WORK_SHOP, I_LD_ID, S_LD_ID, LD_TYPE, MAIN_QX, LAN_POS_MAX, SCRAP_NO,
            DEP_TIME, SHAKER, TRANSFER_ID, AR_O, SHIFT, GROUP1, MAC_CODE,
            FG_NORMAL, LAN_POS_MIN, FURNACE_TIMS, STL_PORT_TIMS, GUN_ONE_TIMS, GUN_TWO_TIMS,
            PLAN_STL_GRD, STL_GRD, STL_ROLL_TMP, MAT_QUL_CD, VENT_STATUS,
            IN_SCRAP_WGT, IRON_GROSS_WGT, IRON_NET_WGT, SCRAP_WGT, TOTAL_SCRAP, IRONSCWGT,
            IRON_C, SI, S, MN, P, IRON_TI, IRON_TMP, IRON_TIME,
            OX_START, OX_TIME, BO_CSM,
            FINISH_C, FINISH_MN, FINISH_P, FINISH_S, FINISH_TMP,
            TSC_TEMP, TSC_C, TSO_TEMP, TSO_C, TSO_O, BLOW_TIME, BLOW_NUMBER,
            TAP_TIME, DZINFOMATION, SLAG_SPLASHING_TIME, NO_CSM,
            AR_TMP_BEFORE, AR_TMP_AFTER, AR_TIME, AR_TMP,
            BOF_C, BOF_MN, BOF_SI, BOF_S, BOF_P, BOF_V, BOF_AL, BOF_N, BOF_CA,
            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA,
            M_C, M_MN, M_SI, M_S, M_P, M_AL,
            STL_WGT, REMARK, ZZ_SCRAP_WGT, ZZ_PI_WGT, SLAB_WEIGHT,
            RE_TIME, RE_NUM_COM
        )
        SELECT
            H.STAR_CAST_TIM,
            A.HEAT_ID,
            'L2',
            A.I_LD_ID, A.S_LD_ID, A.LD_TYPE,
            DECODE(P.LF_NO, NULL, '直上', '精炼'),
            A.LAN_POS_MAX, A.SCRAP_NO,
            A.DEP_TIME, A.SHAKER, A.TRANSFER_ID, A.AR_O, A.SHIFT, A.GROUP1, A.MAC_CODE,
            A.FG_NORMAL, A.LAN_POS_MIN, A.FURNACE_TIMS, A.STL_PORT_TIMS, A.GUN_ONE_TIMS, A.GUN_TWO_TIMS,
            A.PLAN_STL_GRD, A.STL_GRD, A.STL_ROLL_TMP, H.MAT_QUL_CD, A.VENT_STATUS,
            N.IN_SCRAP_WGT, N.IRON_GROSS_WGT, N.IRON_NET_WGT, N.SCRAP_WGT, A.TOTAL_SCRAP, (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)),
            B.C, B.SI, B.S, B.MN, B.P, B.TI, A.IRON_TMP, A.IRON_TIME,
            A.OX_START, A.OX_TIME, A.BO_CSM,
            J.C, J.MN, J.P, J.S, (A.TSO_TEMP+A.BLOW_TIME),
            A.TSC_TEMP, A.TSC_C, A.TSO_TEMP, A.TSO_C, A.TSO_O, A.BLOW_TIME, A.BLOW_NUMBER,
            ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60), A.JB_INFO,
            ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60), A.NO_CSM,
            '', '', AR_TIME, A.AR_TMP,
            D.C, D.Mn, D.Si, D.S, D.P, D.V, D.AL, D.N, D.Ca,
            C.C, C.Mn, C.Si, C.S, C.P, C.V, C.AL, C.N, C.Ca,
            E.C, E.Mn, E.Si, E.S, E.P, E.AL,
            A.STL_WGT, A.REMARK, M.ZZ_SCRAP_WGT, M.ZZ_PI_WGT, F.SLAB_WEIGHT,
            Q.RE_TIME, Q.RE_NUM_COM
        FROM JGLGMES.SMES_B_BOF_RES A
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240601') B ON A.IRON_NO = B.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240601') N ON A.IRON_NO = N.IRON_NO
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'M%') E ON A.HEAT_ID = E.HEAT_ID
        LEFT JOIN (SELECT * FROM JGLGMES.SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM JGLGMES.SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
        LEFT JOIN (SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM JGLGMES.smes_b_cutres GROUP BY SUBSTR(HEAT_ID,0,9)) F ON A.HEAT_ID = F.HEAT_ID
        LEFT JOIN (SELECT heat_id, MAX(MAT_QUL_CD) MAT_QUL_CD, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM JGLGMES.SMES_B_CCMRES
                   WHERE MAC_CODE IN ('2D312','2D313')
                   GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
        LEFT JOIN (SELECT SEQ_NO, NVL(RECYCLED_STEEL,ZZ_SCRAP_WGT) ZZ_SCRAP_WGT, NVL(SINCE_CYCLE,ZZ_PI_WGT) ZZ_PI_WGT FROM JGLGMES.SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
        LEFT JOIN JGLGMES.SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
        LEFT JOIN (SELECT HEAT_ID,MAX(RE_TIME) AS RE_TIME,MAX(RE_NUM_COM) AS RE_NUM_COM FROM JGLGMES.SMES_B_ENER_USEC GROUP BY HEAT_ID) Q ON A.HEAT_ID = Q.HEAT_ID
        WHERE A.WORK_SHOP = 'L2'
          AND H.STAR_CAST_TIM BETWEEN #{date} AND #{date} + 1
    </insert>

    <delete id="deleteByDate" parameterType="java.util.Date">
        DELETE FROM JGLGMES.CARBON_LGMS_BOF_REPORTER
        WHERE TRUNC(P_DATE) = TRUNC(#{date})
    </delete>

    <!-- 清空表数据 -->
    <delete id="truncate">
        TRUNCATE TABLE JGLGMES.CARBON_LGMS_BOF_REPORTER
    </delete>

</mapper>

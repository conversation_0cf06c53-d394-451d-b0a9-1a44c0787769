INSERT INTO CARBON_LGMS_L2_LF_REPORTER (
    LF_DATE, MAC_CODE, HEAT_ID, PLAN_STL_GRD, STL_GRD, SHIFT, GROUP1, EMP_CD, MASTER_EMP, POSITION,
    S_LD_IDCON, S_LD_ID, S_LD_TIMS, VENT_STATUS, ARR_TIME, STR_TIME, END_TIME, DIFF_TIME,
    SLAG_THICK, DEP_TIME, SLAG_FL, REMARK_LF, ARR_TMP, FINISH_TMP, ELEC_AMONT, SOFTBLOWTIME,
    ARR_O, DEP_O, CCM_MAC_CODE, LADLE_ARR_TMP, LADLE_ARR_TIME, STAR_CAST_TIM, END_CAST_TIM, LD_DEP_TIM,
    YAG_TIME, TUNDISH_TEMP_AVG1, TUNDISH_TEMP_AVG2, TUNDISH_TEMP_AVG3, MAT_QUL_CD, GRD_TEMP,
    CASI_WIRE, CAFE_WIRE, MID_CARBON_TI_FE, MID_CARBON_SI_FE, MN_AL_ALLOY, HIGH_CARBON_TI_FE, AL_WIRE, METAL_CA_WIRE,
    FE_CA_WIRE, CARBON_WIRE, CARBON_CORE_WIRE, GRAPHITE_ELECTRODE, LADLE_COVER_AGENT, CARBON_ADDITIVE,
    COAL_CARBON_ADDITIVE, LOW_N_CARBON_ADDITIVE, PETRO_CARBON_ADDITIVE, LIGHT_DOLOMITE, RAW_DOLOMITE,
    HIGH_CA_ASH, LIME, FLUORITE, REFINING_SLAG, CALCIUM_CARBIDE, AL_GRANULE, COMPLEX_DEOXIDIZER,
    AL_BLOCK, AL_ROD, STEEL_CORE_AL, AL_CA_BALL, CARBON_POWDER, SIFE_ALLOY, SIMN_ALLOY,
    LOW_CARBON_MN_FE, MID_CARBON_MN_FE, HIGH_CARBON_MN_FE, TI_FE_ALLOY, NB_FE, SI_C_ALLOY,
    STEEL_SAND_AL, SI_CA_BA, SILICON_CARBIDE, V_N_ALLOY, HIGH_CARBON_CR_FE, LOW_CARBON_CR_FE,
    B_FE, NI_CU_FE, AL_SCRAP, AL_WIRE_2, V_N_ALLOY_2, NEW_COMPLEX_SLAG_AGENT,
    SI_TI_ALLOY, NEW_FOAMING_AGENT, MN_C_ALLOY, TI_FE_CORE_WIRE, SMALL_AL_INGOT,
    HIGH_CA_ACTIVE_LIME, SIMN_ALLOY_5513, HIGH_CA_REFINING_DESULFURIZER,
    BOF_C, BOF_SI, BOF_MN, BOF_P, BOF_S, BOF_AL, BOF_TI,
    LF_C, LF_SI, LF_MN, LF_P, LF_S, LF_AL, LF_V, LF_TI, LF_CA,
    CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA, CCM_TI,
    CCM_CU, CCM_NI, CCM_MO, CCM_B, CCM_N,
    CAST_WGT, SLAB_WEIGHT
)
SELECT
    H.STAR_CAST_TIM,                    -- LF_DATE
    A.MAC_CODE,                         -- MAC_CODE
    A.HEAT_ID,                          -- HEAT_ID
    A.PLAN_STL_GRD,                     -- PLAN_STL_GRD
    H.STL_GRD_CD,                       -- STL_GRD
    A.SHIFT,                            -- SHIFT
    A.GROUP1,                           -- GROUP1
    A.EMP_CD,                           -- EMP_CD
    A.MASTER_EMP,                       -- MASTER_EMP
    A.POSITION,                         -- POSITION
    (SELECT cd_name FROM BBB_CD T WHERE CD_MANA_NO='S0017' AND cd= A.S_LD_IDCON), -- S_LD_IDCON
    A.S_LD_ID,                          -- S_LD_ID
    A.S_LD_TIMS,                        -- S_LD_TIMS
    A.VENT_STATUS,                      -- VENT_STATUS
    A.ARR_TIME,                         -- ARR_TIME
    A.STR_TIME,                         -- STR_TIME
    A.END_TIME,                         -- END_TIME
    ROUND(TO_NUMBER(A.END_TIME - A.STR_TIME) * 24 * 60), -- DIFF_TIME
    A.SLAG_THICK,                       -- SLAG_THICK
    A.DEP_TIME,                         -- DEP_TIME
    A.SLAG_FL,                          -- SLAG_FL
    A.REMARK_LF,                        -- REMARK_LF
    A.ARR_TMP,                          -- ARR_TMP
    A.FINISH_TMP,                       -- FINISH_TMP
    A.ELEC_AMONT,                       -- ELEC_AMONT
    A.SOFTBLOWTIME,                     -- SOFTBLOWTIME
    A.ARR_O,                            -- ARR_O
    A.DEP_O,                            -- DEP_O
    H.MAC_CODE,                         -- CCM_MAC_CODE
    H.LADLE_ARR_TMP,                    -- LADLE_ARR_TMP
    H.LADLE_ARR_TIME,                   -- LADLE_ARR_TIME
    H.STAR_CAST_TIM,                    -- STAR_CAST_TIM
    H.END_CAST_TIM,                     -- END_CAST_TIM
    H.LD_DEP_TIM,                       -- LD_DEP_TIM
    ROUND(TO_NUMBER(H.STAR_CAST_TIM - H.LADLE_ARR_TIME) * 24 * 60), -- YAG_TIME
    H.TUNDISH_TEMP_AVG1,                -- TUNDISH_TEMP_AVG1
    H.TUNDISH_TEMP_AVG2,                -- TUNDISH_TEMP_AVG2
    H.TUNDISH_TEMP_AVG3,                -- TUNDISH_TEMP_AVG3
    H.MAT_QUL_CD,                       -- MAT_QUL_CD
    '',                                 -- GRD_TEMP
    G.CaSi线,                           -- CASI_WIRE
    G.CaFe线,                           -- CAFE_WIRE
    G.中碳钛铁,                         -- MID_CARBON_TI_FE
    G.中碳硅铁,                         -- MID_CARBON_SI_FE
    G.锰铝合金,                         -- MN_AL_ALLOY
    G.高碳钛铁,                         -- HIGH_CARBON_TI_FE
    G.铝线,                             -- AL_WIRE
    G.金属钙线,                         -- METAL_CA_WIRE
    G.铁钙线,                           -- FE_CA_WIRE
    G.碳线,                             -- CARBON_WIRE
    G.碳包芯线,                         -- CARBON_CORE_WIRE
    G.石墨电极,                         -- GRAPHITE_ELECTRODE
    G.钢包覆盖剂,                       -- LADLE_COVER_AGENT
    G.增碳剂,                           -- CARBON_ADDITIVE
    G.煤质增碳剂,                       -- COAL_CARBON_ADDITIVE
    G.低氮增碳剂,                       -- LOW_N_CARBON_ADDITIVE
    G.石油焦增碳剂,                     -- PETRO_CARBON_ADDITIVE
    G.轻烧白云石,                       -- LIGHT_DOLOMITE
    G.生白云石,                         -- RAW_DOLOMITE
    G.高钙灰,                           -- HIGH_CA_ASH
    G.石灰,                             -- LIME
    G.萤石,                             -- FLUORITE
    G.精炼渣,                           -- REFINING_SLAG
    G.电石,                             -- CALCIUM_CARBIDE
    G.铝粒,                             -- AL_GRANULE
    G.复脱,                             -- COMPLEX_DEOXIDIZER
    G.铝块,                             -- AL_BLOCK
    G.铝棒,                             -- AL_ROD
    G.钢芯铝,                           -- STEEL_CORE_AL
    G.铝钙球,                           -- AL_CA_BALL
    G.碳粉,                             -- CARBON_POWDER
    G.硅铁,                             -- SIFE_ALLOY
    G.硅锰合金,                         -- SIMN_ALLOY
    G.低碳锰铁,                         -- LOW_CARBON_MN_FE
    G.中碳锰铁,                         -- MID_CARBON_MN_FE
    G.高碳锰铁,                         -- HIGH_CARBON_MN_FE
    G.钛铁合金,                         -- TI_FE_ALLOY
    G.铌铁,                             -- NB_FE
    G.硅碳合金,                         -- SI_C_ALLOY
    G.钢砂铝,                           -- STEEL_SAND_AL
    G.硅钙钡,                           -- SI_CA_BA
    G.碳化硅,                           -- SILICON_CARBIDE
    G.钒氮合金,                         -- V_N_ALLOY
    G.高碳铬铁,                         -- HIGH_CARBON_CR_FE
    G.低碳铬铁,                         -- LOW_CARBON_CR_FE
    G.硼铁,                             -- B_FE
    G.镍铜铁,                           -- NI_CU_FE
    G.铝屑,                             -- AL_SCRAP
    G.铝线,                             -- AL_WIRE_2
    G.钒氮合金,                         -- V_N_ALLOY_2
    G.新型复合化渣剂,                   -- NEW_COMPLEX_SLAG_AGENT
    G.硅钛合金,                         -- SI_TI_ALLOY
    G.新型发泡剂,                       -- NEW_FOAMING_AGENT
    G.锰碳合金,                         -- MN_C_ALLOY
    G.钛铁包芯线,                       -- TI_FE_CORE_WIRE
    G.小铝锭,                           -- SMALL_AL_INGOT
    G.高钙活性石灰,                     -- HIGH_CA_ACTIVE_LIME
    G.硅锰合金5513,                     -- SIMN_ALLOY_5513
    G.高钙复合精炼脱硫剂,               -- HIGH_CA_REFINING_DESULFURIZER
    E.C,                                -- BOF_C
    E.Si,                               -- BOF_SI
    E.Mn,                               -- BOF_MN
    E.P,                                -- BOF_P
    E.S,                                -- BOF_S
    E.AL,                               -- BOF_AL
    E.TI,                               -- BOF_TI
    D.C,                                -- LF_C
    D.Si,                               -- LF_SI
    D.Mn,                               -- LF_MN
    D.P,                                -- LF_P
    D.S,                                -- LF_S
    D.AL,                               -- LF_AL
    D.V,                                -- LF_V
    D.TI,                               -- LF_TI
    D.CA,                               -- LF_CA
    C.C,                                -- CCM_C
    C.Mn,                               -- CCM_MN
    C.Si,                               -- CCM_SI
    C.S,                                -- CCM_S
    C.P,                                -- CCM_P
    C.V,                                -- CCM_V
    C.ALS,                              -- CCM_ALS
    C.AL,                               -- CCM_ALT
    C.CA,                               -- CCM_CA
    C.TI,                               -- CCM_TI
    C.CU,                               -- CCM_CU
    C.NI,                               -- CCM_NI
    C.MO,                               -- CCM_MO
    C.B,                                -- CCM_B
    C.N,                                -- CCM_N
    H.CAST_WGT,                         -- CAST_WGT
    F.SLAB_WEIGHT                       -- SLAB_WEIGHT
FROM SMES_B_LF_RES A,
     (select *
      from SMES_B_che_steel a
      where a.SEQ_NO IN (select max(B.SEQ_NO)
                         from SMES_B_che_steel b
                         where a.HEAT_ID = B.HEAT_ID
                         GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
        and sampletype like 'C%') C,
     (select *
      from SMES_B_che_steel a
      where a.SEQ_NO IN (select max(B.SEQ_NO)
                         from SMES_B_che_steel b
                         where a.HEAT_ID = B.HEAT_ID
                         GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
        and sampletype like 'J%') D,
     (select *
      from SMES_B_che_steel a
      where a.SEQ_NO IN (select max(B.SEQ_NO)
                         from SMES_B_che_steel b
                         where a.HEAT_ID = B.HEAT_ID
                         GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
        and sampletype like 'L%') E,
     (select HEAT_ID,
             TREAT_NO,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) CaSi线,
             SUM(DECODE(T.MAT_CODE, '10201017', T.MAT_WGT, '')) 锰铝合金,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) CaFe线,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 中碳钛铁,
             SUM(DECODE(T.MAT_CODE, '10201010', T.MAT_WGT, '')) 中碳硅铁,
             SUM(DECODE(T.MAT_CODE, '10201011', T.MAT_WGT, '')) 高碳钛铁,
             SUM(DECODE(T.MAT_CODE, '10202002', T.MAT_WGT, '')) 铝线,
             SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, '')) 金属钙线,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 铁钙线,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 碳线,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 碳包芯线,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 石墨电极,
             SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, '')) 钢包覆盖剂,
             SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, '')) 增碳剂,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 煤质增碳剂,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 低氮增碳剂,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 石油焦增碳剂,
             SUM(DECODE(T.MAT_CODE, '10705003', T.MAT_WGT, '10501002', T.MAT_WGT, '')) 轻烧白云石,
             SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT,'10501029', T.MAT_WGT, '')) 生白云石,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 高钙灰,
             SUM(DECODE(T.MAT_CODE, '10501024', T.MAT_WGT,'10705001', T.MAT_WGT, '')) 石灰,
             SUM(DECODE(T.MAT_CODE, '10502009', T.MAT_WGT, '')) 萤石,
             SUM(DECODE(T.MAT_CODE, '10502002', T.MAT_WGT, '')) 精炼渣,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 电石,
             SUM(DECODE(T.MAT_CODE, '10202003', T.MAT_WGT, '')) 铝粒,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 复脱,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 铝块,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 铝棒,
             SUM(DECODE(T.MAT_CODE, '10202001', T.MAT_WGT, '')) 钢芯铝,
             SUM(DECODE(T.MAT_CODE, '10202005', T.MAT_WGT, '')) 铝钙球,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 碳粉,
             SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, '')) 硅铁,
             SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, '')) 硅锰合金,
             SUM(DECODE(T.MAT_CODE, '10201012', T.MAT_WGT, '')) 低碳锰铁,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 中碳锰铁,
             SUM(DECODE(T.MAT_CODE, '10201004', T.MAT_WGT,'10201005', T.MAT_WGT, '')) 高碳锰铁,
             SUM(DECODE(T.MAT_CODE, '10201007', T.MAT_WGT, '')) 钛铁合金,
             SUM(DECODE(T.MAT_CODE, '10201016', T.MAT_WGT, '')) 铌铁,
             SUM(DECODE(T.MAT_CODE, '10201008', T.MAT_WGT, '')) 硅碳合金,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 钢砂铝,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 硅钙钡,
             SUM(DECODE(T.MAT_CODE, '10202010', T.MAT_WGT, '')) 碳化硅,
             SUM(DECODE(T.MAT_CODE, '10201003', T.MAT_WGT, '')) 钒氮合金,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 高碳铬铁,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 低碳铬铁,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 硼铁,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 镍铜铁,
             SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, '')) 铝屑,
             SUM(DECODE(T.MAT_CODE, '10502006', T.MAT_WGT, '')) 新型复合化渣剂,
             SUM(DECODE(T.MAT_CODE, '10201002', T.MAT_WGT, '')) 硅钛合金,
             SUM(DECODE(T.MAT_CODE, '10502001', T.MAT_WGT, '')) 新型发泡剂,
             SUM(DECODE(T.MAT_CODE, '10201020', T.MAT_WGT, '')) 锰碳合金,
             SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, '')) 钛铁包芯线,
             SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, '')) 小铝锭,
             SUM(DECODE(T.MAT_CODE, '10501043', T.MAT_WGT, '')) 高钙活性石灰,
             SUM(DECODE(T.MAT_CODE, '10201024', T.MAT_WGT, '')) 硅锰合金5513,
             SUM(DECODE(T.MAT_CODE, '10503005', T.MAT_WGT, '')) 高钙复合精炼脱硫剂
      from SMES_B_MAT_USE t
      where T.wp_code = '2C02'
      group by HEAT_ID, TREAT_NO) G,
     (select heat_id,
             TREAT_NO,
             max(MAT_QUL_CD) MAT_QUL_CD,
             max(STL_GRD_CD) STL_GRD_CD,
             max(PLAN_HEAT_ID) PLAN_HEAT_ID,
             MAX(STAR_CAST_TIM) STAR_CAST_TIM,
             MAX(MAC_CODE) MAC_CODE,
             MAX(LADLE_ARR_TMP) LADLE_ARR_TMP,
             MAX(LADLE_ARR_TIME) LADLE_ARR_TIME,
             MAX(END_CAST_TIM) END_CAST_TIM,
             MAX(LD_DEP_TIM) LD_DEP_TIM,
             MAX(TUNDISH_TEMP_AVG1) TUNDISH_TEMP_AVG1,
             MAX(TUNDISH_TEMP_AVG2) TUNDISH_TEMP_AVG2,
             MAX(TUNDISH_TEMP_AVG3) TUNDISH_TEMP_AVG3,
             SUM(CAST_WGT) CAST_WGT
      from SMES_B_CCMRES
      WHERE MAC_CODE IN ('2D312','2D313')
      GROUP BY HEAT_ID, TREAT_NO) H,
     (select SUBSTR(HEAT_ID,0,9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT
      from smes_b_cutres
      group by SUBSTR(HEAT_ID,0,9)) F
WHERE A.HEAT_ID = C.HEAT_ID(+)
  AND A.HEAT_ID = D.HEAT_ID(+)
  AND A.HEAT_ID = E.HEAT_ID(+)
  AND A.HEAT_ID = G.HEAT_ID(+)
  AND A.TREAT_NO = G.TREAT_NO(+)
  AND A.HEAT_ID = H.HEAT_ID(+)
  AND A.TREAT_NO = H.TREAT_NO(+)
  AND A.HEAT_ID = F.HEAT_ID(+)
  AND A.WORK_SHOP='L2'
  AND TO_CHAR(H.STAR_CAST_TIM, 'YYYY-MM-DD HH24:MI:SS') BETWEEN '2025-08-09 00:00:00' AND '2025-08-10 00:00:00'
ORDER BY A.ARR_TIME;
-- 转炉报表数据插入SQL (完整版)
-- 将一钢和二钢的原始查询数据插入到统一表CONVERTER_REPORT中
-- 创建时间: 2025-08-09

-- ========================================
-- 一钢数据插入SQL (WORK_SHOP = 'L1')
-- ========================================
INSERT INTO CARBON_LGMS_BOF_REPORTER (
    P_DATE, HEAT_ID, WORK_SHOP, S_LD_ID, MAIN_QX,
    DEP_TIME, SHAKER, TRANSFER_ID, AR_O, SHIFT, GROUP1, MAC_CODE,
    FG_NORMAL, LAN_POS_MIN, FURNACE_TIMS, STL_PORT_TIMS, GUN_ONE_TIMS, GUN_TWO_TIMS,
    PLAN_STL_GRD, STL_GRD, VENT_STATUS, MEMO2, MEMO3,
    TAP_START, TAP_END, TAP_TIME, PLAN_CCM_NO,
    OX_START, OX_END, OUT_TPM, BLOW_TIME, BLOW_NUMBER,
    IRON_WGT, SCRAP_WGT, TOTAL_SCRAP, IRONSCWGT,
    IRON_C, SI, S, MN, P, IRON_TI, IRON_TMP, IRON_TIME,
    OX_TIME, BO_CSM,
    FINISH_C, FINISH_MN, FINISH_P, FINISH_S, FINISH_SI, FINISH_TMP,
    TSC_TEMP, TSC_C, TSO_TEMP, TSO_C, TSO_O,
    SLAG_SPLASHING_TIME, NO_CSM, DZINFOMATION,
    AR_TMP_BEFORE, AR_TMP_AFTER, AR_TIME, AR_TMP,
    BOF_C, BOF_MN, BOF_SI, BOF_S, BOF_P, BOF_V, BOF_AL, BOF_N, BOF_CA,
    CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA,
    LIMESTONE, DOLOMITE, LADLE_COVER, SLAG_MODIFIER, LIME_BLOCK, LIGHT_DOLOMITE,
    COARSE_PARTICLE, IRON_SCALE, FLUX, SI_MN_ALLOY, SI_FE_ALLOY, V_N_ALLOY,
    SMALL_AL, COMPLEX_ALLOY, CARBURIZER, CA_WIRE, COLD_MATERIAL, SINTER_ORE,
    BF_RETURN, TI_FE_ALLOY, O2_TUBE, TI_FE_WIRE, COMPREHENSIVE_SCRAP,
    SELF_CYCLE, KILN_SLAG, MAG_POWDER, STONE, SELF_RETURN_COKE,
    STL_WGT, REMARK, ZZ_SCRAP_WGT, ZZ_PI_WGT, SLAB_WEIGHT
)
SELECT
    H.STAR_CAST_TIM,                    -- P_DATE
    A.HEAT_ID,                          -- HEAT_ID
    'L1',                               -- WORK_SHOP
    A.S_LD_ID,                          -- S_LD_ID
    DECODE(P.LF_NO, NULL, '直上', '精炼'), -- MAIN_QX
    A.DEP_TIME,                         -- DEP_TIME
    A.SHAKER,                           -- SHAKER
    A.TRANSFER_ID,                      -- TRANSFER_ID
    A.AR_O,                             -- AR_O
    A.SHIFT,                            -- SHIFT
    A.GROUP1,                           -- GROUP1
    A.MAC_CODE,                         -- MAC_CODE
    A.FG_NORMAL,                        -- FG_NORMAL
    A.LAN_POS_MIN,                      -- LAN_POS_MIN
    A.FURNACE_TIMS,                     -- FURNACE_TIMS
    A.STL_PORT_TIMS,                    -- STL_PORT_TIMS
    A.GUN_ONE_TIMS,                     -- GUN_ONE_TIMS
    A.GUN_TWO_TIMS,                     -- GUN_TWO_TIMS
    A.PLAN_STL_GRD,                     -- PLAN_STL_GRD
    A.STL_GRD,                          -- STL_GRD
    A.VENT_STATUS,                      -- VENT_STATUS
    A.MEMO2,                            -- MEMO2
    A.MEMO3,                            -- MEMO3
    A.TAP_START,                        -- TAP_START
    A.TAP_END,                          -- TAP_END
    ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60), -- TAP_TIME
    A.PLAN_CCM_NO,                      -- PLAN_CCM_NO
    A.OX_START,                         -- OX_START
    A.OX_END,                           -- OX_END
    A.OUT_TPM,                          -- OUT_TPM
    A.BLOW_TIME,                        -- BLOW_TIME
    A.BLOW_NUMBER,                      -- BLOW_NUMBER
    A.IRON_WGT,                         -- IRON_WGT
    N.SCRAP_WGT,                        -- SCRAP_WGT
    A.TOTAL_SCRAP,                      -- TOTAL_SCRAP
    (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)), -- IRONSCWGT
    B.C,                                -- IRON_C
    B.SI,                               -- SI
    B.S,                                -- S
    B.MN,                               -- MN
    B.P,                                -- P
    B.TI,                               -- IRON_TI
    A.IRON_TMP,                         -- IRON_TMP
    A.IRON_TIME,                        -- IRON_TIME
    A.OX_TIME,                          -- OX_TIME
    A.BO_CSM,                           -- BO_CSM
    J.C,                                -- FINISH_C
    J.MN,                               -- FINISH_MN
    J.P,                                -- FINISH_P
    J.S,                                -- FINISH_S
    J.SI,                               -- FINISH_SI
    (A.TSO_TEMP + A.BLOW_TIME),         -- FINISH_TMP
    A.TSC_TEMP,                         -- TSC_TEMP
    A.TSC_C,                            -- TSC_C
    A.TSO_TEMP,                         -- TSO_TEMP
    A.TSO_C,                            -- TSO_C
    A.TSO_O,                            -- TSO_O
    ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60), -- SLAG_SPLASHING_TIME
    A.NO_CSM,                           -- NO_CSM
    A.JB_INFO,                          -- DZINFOMATION
    '',                                 -- AR_TMP_BEFORE
    '',                                 -- AR_TMP_AFTER
    AR_TIME,                            -- AR_TIME
    A.AR_TMP,                           -- AR_TMP
    D.C,                                -- BOF_C
    D.Mn,                               -- BOF_MN
    D.Si,                               -- BOF_SI
    D.S,                                -- BOF_S
    D.P,                                -- BOF_P
    D.V,                                -- BOF_V
    D.AL,                               -- BOF_AL
    D.N,                                -- BOF_N
    D.Ca,                               -- BOF_CA
    C.C,                                -- CCM_C
    C.Mn,                               -- CCM_MN
    C.Si,                               -- CCM_SI
    C.S,                                -- CCM_S
    C.P,                                -- CCM_P
    C.V,                                -- CCM_V
    C.AL,                               -- CCM_AL
    C.N,                                -- CCM_N
    C.Ca,                               -- CCM_CA
    G.石灰石,                            -- LIMESTONE
    G.白云石,                            -- DOLOMITE
    G.钢包覆盖剂,                        -- LADLE_COVER
    G.钢渣改质剂,                        -- SLAG_MODIFIER
    G.石灰块,                            -- LIME_BLOCK
    G.轻烧白云石,                        -- LIGHT_DOLOMITE
    G.粗颗粒,                            -- COARSE_PARTICLE
    G.氧化铁皮,                          -- IRON_SCALE
    G.高效复合助熔剂,                    -- FLUX
    G.硅锰合金,                          -- SI_MN_ALLOY
    G.硅铁,                              -- SI_FE_ALLOY
    G.钒氮合金,                          -- V_N_ALLOY
    G.小铝锭,                            -- SMALL_AL
    G.复析合金,                          -- COMPLEX_ALLOY
    G.增碳剂,                            -- CARBURIZER
    G.金属钙线,                          -- CA_WIRE
    G.冷料,                              -- COLD_MATERIAL
    G.烧结矿,                            -- SINTER_ORE
    G.高炉返矿,                          -- BF_RETURN
    G.钛铁,                              -- TI_FE_ALLOY
    G.定氧管,                            -- O2_TUBE
    G.钛铁包芯线,                        -- TI_FE_WIRE
    DECODE(round((G.综合废钢 + G.综合废钢2)/1000,3),0,M.ZZ_SCRAP_WGT,round((G.综合废钢 + G.综合废钢2)/1000,3)), -- COMPREHENSIVE_SCRAP
    round(G.自循环/1000,3),              -- SELF_CYCLE
    G.窑渣,                              -- KILN_SLAG
    G.磁选精粉,                          -- MAG_POWDER
    G.石子,                              -- STONE
    G.自返焦粉,                          -- SELF_RETURN_COKE
    A.STL_WGT,                          -- STL_WGT
    A.REMARK,                           -- REMARK
    M.ZZ_SCRAP_WGT,                     -- ZZ_SCRAP_WGT
    M.ZZ_PI_WGT,                        -- ZZ_PI_WGT
    F.SLAB_WEIGHT                      -- SLAB_WEIGHT
FROM SMES_B_BOF_RES A
         LEFT JOIN (SELECT * FROM SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240101') B ON A.HEAT_ID = B.HEAT_ID
         LEFT JOIN (SELECT * FROM SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240101') N ON A.IRON_NO = N.IRON_NO
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
         LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM smes_b_cutres GROUP BY SUBSTR(HEAT_ID, 0, 9)) F ON A.HEAT_ID = F.HEAT_ID
         LEFT JOIN (SELECT T.HEAT_ID,
                           SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT,'10501012', T.MAT_WGT, 0)) 石灰石,
                           SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT,'10501029', T.MAT_WGT, 0)) 白云石,
                           SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, 0)) 钢包覆盖剂,
                           SUM(DECODE(T.MAT_CODE, '10502008', T.MAT_WGT, 0)) 钢渣改质剂,
                           SUM(DECODE(T.MAT_CODE, '10705001', T.MAT_WGT,'10501024', T.MAT_WGT, 0)) 石灰块,
                           SUM(DECODE(T.MAT_CODE, '10501002', T.MAT_WGT,'10705003', T.MAT_WGT, 0)) 轻烧白云石,
                           SUM(DECODE(T.MAT_CODE, '10604002', T.MAT_WGT, 0)) 粗颗粒,
                           SUM(DECODE(T.MAT_CODE, '10606001', T.MAT_WGT, 0)) 氧化铁皮,
                           SUM(DECODE(T.MAT_CODE, '10502003', T.MAT_WGT, 0)) 高效复合助熔剂,
                           SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰合金,
                           SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                           SUM(DECODE(T.MAT_CODE, '10201003', T.MAT_WGT, 0)) 钒氮合金,
                           SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 小铝锭,
                           SUM(DECODE(T.MAT_CODE, '10201009', T.MAT_WGT, 0)) 复析合金,
                           SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 增碳剂,
                           SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 金属钙线,
                           SUM(DECODE(T.MAT_CODE, '10604017', T.MAT_WGT, 0)) 冷料,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 烧结矿,
                           SUM(DECODE(T.MAT_CODE, '10603001', T.MAT_WGT, 0)) 高炉返矿,
                           SUM(DECODE(T.MAT_CODE, '10201007', T.MAT_WGT, 0)) 钛铁,
                           SUM(DECODE(T.MAT_CODE, '10504022', T.MAT_WGT, 0)) 定氧管,
                           SUM(DECODE(T.MAT_CODE, '10202011', T.MAT_WGT, 0)) 钛铁包芯线,
                           SUM(DECODE(T.MAT_CODE, '10501035', T.MAT_WGT,'10501036', T.MAT_WGT, 0)) 石子,
                           SUM(DECODE(T.MAT_CODE, '10401014', T.MAT_WGT, 0)) 综合废钢,
                           SUM(DECODE(T.MAT_CODE, '10401015', T.MAT_WGT, 0)) 综合废钢2,
                           SUM(DECODE(T.MAT_CODE, '1001002', T.MAT_WGT,'1001001', T.MAT_WGT, 0)) 自循环,
                           SUM(DECODE(T.MAT_CODE, '10701002', T.MAT_WGT, 0)) 窑渣,
                           SUM(DECODE(T.MAT_CODE, '10607001', T.MAT_WGT, 0)) 磁选精粉,
                           SUM(DECODE(T.MAT_CODE, '10603008', T.MAT_WGT, 0)) 自返焦粉
                    FROM SMES_B_MAT_USE t WHERE T.wp_code = '1C01' GROUP BY T.HEAT_ID) G ON A.HEAT_ID = G.HEAT_ID
         LEFT JOIN (SELECT heat_id, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM SMES_B_CCMRES
                    where MAC_CODE in ('1D311','1D313','1D314')
                    GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
         LEFT JOIN (SELECT SEQ_NO, ZZ_SCRAP_WGT, ZZ_PI_WGT FROM SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
         LEFT JOIN SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
WHERE A.WORK_SHOP = 'L1'
  and A.MAC_CODE != '1C015'
  AND H.STAR_CAST_TIM BETWEEN TO_DATE('2025-08-09 00:00:00' ,'YYYY-MM-DD HH24:MI:SS') AND TO_DATE('2025-08-10 00:00:00' ,'YYYY-MM-DD HH24:MI:SS');
-- 二钢数据插入SQL (WORK_SHOP = 'L2')
-- ========================================

INSERT INTO CARBON_LGMS_BOF_REPORTER (
    P_DATE, HEAT_ID, WORK_SHOP, I_LD_ID, S_LD_ID, LD_TYPE, MAIN_QX, LAN_POS_MAX, SCRAP_NO,
    DEP_TIME, SHAKER, TRANSFER_ID, AR_O, SHIFT, GROUP1, MAC_CODE,
    FG_NORMAL, LAN_POS_MIN, FURNACE_TIMS, STL_PORT_TIMS, GUN_ONE_TIMS, GUN_TWO_TIMS,
    PLAN_STL_GRD, STL_GRD, STL_ROLL_TMP, MAT_QUL_CD, VENT_STATUS,
    IN_SCRAP_WGT, IRON_GROSS_WGT, IRON_NET_WGT, SCRAP_WGT, TOTAL_SCRAP, IRONSCWGT,
    IRON_C, SI, S, MN, P, IRON_TI, IRON_TMP, IRON_TIME,
    OX_START, OX_TIME, BO_CSM,
    FINISH_C, FINISH_MN, FINISH_P, FINISH_S, FINISH_TMP,
    TSC_TEMP, TSC_C, TSO_TEMP, TSO_C, TSO_O, BLOW_TIME, BLOW_NUMBER,
    TAP_TIME, DZINFOMATION, SLAG_SPLASHING_TIME, NO_CSM,
    AR_TMP_BEFORE, AR_TMP_AFTER, AR_TIME, AR_TMP,
    BOF_C, BOF_MN, BOF_SI, BOF_S, BOF_P, BOF_V, BOF_AL, BOF_N, BOF_CA,
    CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_AL, CCM_N, CCM_CA,
    M_C, M_MN, M_SI, M_S, M_P, M_AL,
    SI_TI_ALLOY, WHITE_LIME, MID_C_SI_FE, MN_AL_ALLOY, FLUX, LIGHT_DOLOMITE,
    RAW_DOLOMITE, LIMESTONE, SINTER_ORE, SINTER_RETURN, IRON_POWDER,
    LIGHT_MG_BALL, HIGH_C_MG_BALL, HIGH_CA_LIME, REFINING_SLAG, COVER_AGENT,
    COKE, FLUORITE_BALL, SI_FE_ALLOY, SI_MN_ALLOY, LOW_AL_SI_FE, HIGH_SI_MN,
    CARBURIZER, COAL_CARBURIZER, LOW_N_CARBURIZER, PETRO_CARBURIZER,
    V_FE_ALLOY, N_V_FE_ALLOY, HIGH_C_MN_FE, TI_FE_ALLOY, NB_FE_ALLOY,
    LOW_C_MN_FE, MID_C_MN_FE, SI_C_ALLOY, STEEL_AL, HIGH_C_CR_FE,
    LOW_C_CR_FE, B_FE_ALLOY, V_N_ALLOY, NI_CU_FE, SI_CARBIDE,
    SI_CA_BA, SI_AL_CA, CA_WIRE, AL_PARTICLE, AL_WIRE, AL_WIRE_METER,
    CA_FE_WIRE, FE_CA_WIRE, C_WIRE, C_CORE_WIRE, AL_BLOCK, AL_BAR,
    STEEL_AL_CORE, AL_CA_BALL, SLAG_CONE, SLAG_PLUG, TEMP_TUBE, O2_TUBE,
    SAMPLE_DEVICE, REBAR_HEAD, NEW_MG_CA_BALL, HIGH_C_TI_FE, NEW_SLAG_AGENT,
    BF_RETURN, COMPREHENSIVE_SCRAP, MN_C_ALLOY, SMALL_AL, HIGH_CA_LIME_ACTIVE,
    SI_MN_55_13, KILN_SLAG, HIGH_CA_DESULFUR,
    STL_WGT, REMARK, ZZ_SCRAP_WGT, ZZ_PI_WGT, SLAB_WEIGHT,
    RE_TIME, RE_NUM_COM
)
SELECT
    H.STAR_CAST_TIM,                    -- P_DATE
    A.HEAT_ID,                          -- HEAT_ID
    'L2',                               -- WORK_SHOP
    A.I_LD_ID,                          -- I_LD_ID
    A.S_LD_ID,                          -- S_LD_ID
    A.LD_TYPE,                          -- LD_TYPE
    DECODE(P.LF_NO, NULL, '直上', '精炼'), -- MAIN_QX
    A.LAN_POS_MAX,                      -- LAN_POS_MAX
    A.SCRAP_NO,                         -- SCRAP_NO
    A.DEP_TIME,                         -- DEP_TIME
    A.SHAKER,                           -- SHAKER
    A.TRANSFER_ID,                      -- TRANSFER_ID
    A.AR_O,                             -- AR_O
    A.SHIFT,                            -- SHIFT
    A.GROUP1,                           -- GROUP1
    A.MAC_CODE,                         -- MAC_CODE
    A.FG_NORMAL,                        -- FG_NORMAL
    A.LAN_POS_MIN,                      -- LAN_POS_MIN
    A.FURNACE_TIMS,                     -- FURNACE_TIMS
    A.STL_PORT_TIMS,                    -- STL_PORT_TIMS
    A.GUN_ONE_TIMS,                     -- GUN_ONE_TIMS
    A.GUN_TWO_TIMS,                     -- GUN_TWO_TIMS
    A.PLAN_STL_GRD,                     -- PLAN_STL_GRD
    A.STL_GRD,                          -- STL_GRD
    A.STL_ROLL_TMP,                     -- STL_ROLL_TMP
    H.MAT_QUL_CD,                       -- MAT_QUL_CD
    A.VENT_STATUS,                      -- VENT_STATUS
    N.IN_SCRAP_WGT,                     -- IN_SCRAP_WGT
    N.IRON_GROSS_WGT,                   -- IRON_GROSS_WGT
    N.IRON_NET_WGT,                     -- IRON_NET_WGT
    N.SCRAP_WGT,                        -- SCRAP_WGT
    A.TOTAL_SCRAP,                      -- TOTAL_SCRAP
    (NVL(A.IRON_WGT, 0) + NVL(A.TOTAL_SCRAP, 0)), -- IRONSCWGT
    B.C,                                -- IRON_C
    B.SI,                               -- SI
    B.S,                                -- S
    B.MN,                               -- MN
    B.P,                                -- P
    B.TI,                               -- IRON_TI
    A.IRON_TMP,                         -- IRON_TMP
    A.IRON_TIME,                        -- IRON_TIME
    A.OX_START,                         -- OX_START
    A.OX_TIME,                          -- OX_TIME
    A.BO_CSM,                           -- BO_CSM
    J.C,                                -- FINISH_C
    J.MN,                               -- FINISH_MN
    J.P,                                -- FINISH_P
    J.S,                                -- FINISH_S
    (A.TSO_TEMP+A.BLOW_TIME),           -- FINISH_TMP
    A.TSC_TEMP,                         -- TSC_TEMP
    A.TSC_C,                            -- TSC_C
    A.TSO_TEMP,                         -- TSO_TEMP
    A.TSO_C,                            -- TSO_C
    A.TSO_O,                            -- TSO_O
    A.BLOW_TIME,                        -- BLOW_TIME
    A.BLOW_NUMBER,                      -- BLOW_NUMBER
    ROUND(TO_NUMBER(A.TAP_END - A.TAP_START) * 24 * 60 * 60), -- TAP_TIME
    A.JB_INFO,                          -- DZINFOMATION
    ROUND(TO_NUMBER(A.SLAG_SPLASHING_END - A.SLAG_SPLASHING_START) * 24 * 60 * 60), -- SLAG_SPLASHING_TIME
    A.NO_CSM,                           -- NO_CSM
    '',                                 -- AR_TMP_BEFORE
    '',                                 -- AR_TMP_AFTER
    AR_TIME,                            -- AR_TIME
    A.AR_TMP,                           -- AR_TMP
    D.C,                                -- BOF_C
    D.Mn,                               -- BOF_MN
    D.Si,                               -- BOF_SI
    D.S,                                -- BOF_S
    D.P,                                -- BOF_P
    D.V,                                -- BOF_V
    D.AL,                               -- BOF_AL
    D.N,                                -- BOF_N
    D.Ca,                               -- BOF_CA
    C.C,                                -- CCM_C
    C.Mn,                               -- CCM_MN
    C.Si,                               -- CCM_SI
    C.S,                                -- CCM_S
    C.P,                                -- CCM_P
    C.V,                                -- CCM_V
    C.AL,                               -- CCM_AL
    C.N,                                -- CCM_N
    C.Ca,                               -- CCM_CA
    E.C,                                -- M_C
    E.Mn,                               -- M_MN
    E.Si,                               -- M_SI
    E.S,                                -- M_S
    E.P,                                -- M_P
    E.AL,                               -- M_AL
    G.硅钛合金,                          -- SI_TI_ALLOY
    G.白灰,                              -- WHITE_LIME
    G.中碳硅铁,                          -- MID_C_SI_FE
    G.锰铝合金,                          -- MN_AL_ALLOY
    G.高效复合助熔剂,                    -- FLUX
    G.轻烧白云石,                        -- LIGHT_DOLOMITE
    G.生白云石,                          -- RAW_DOLOMITE
    G.石灰石,                            -- LIMESTONE
    G.烧结矿,                            -- SINTER_ORE
    G.烧结返矿,                          -- SINTER_RETURN
    G.铁精粉,                            -- IRON_POWDER
    G.轻烧镁球,                          -- LIGHT_MG_BALL
    G.高碳镁球,                          -- HIGH_C_MG_BALL
    G.高钙灰,                            -- HIGH_CA_LIME
    G.精炼预熔渣,                        -- REFINING_SLAG
    G.覆盖剂,                            -- COVER_AGENT
    G.焦炭,                              -- COKE
    G.萤石球,                            -- FLUORITE_BALL
    G.硅铁,                              -- SI_FE_ALLOY
    G.硅锰合金,                          -- SI_MN_ALLOY
    G.低铝硅铁,                          -- LOW_AL_SI_FE
    G.高硅硅锰,                          -- HIGH_SI_MN
    G.增碳剂,                            -- CARBURIZER
    G.煤质增碳剂,                        -- COAL_CARBURIZER
    G.低氮增碳剂,                        -- LOW_N_CARBURIZER
    G.石油焦增碳剂,                      -- PETRO_CARBURIZER
    G.钒铁,                              -- V_FE_ALLOY
    G.氮化钒铁,                          -- N_V_FE_ALLOY
    G.高碳锰铁,                          -- HIGH_C_MN_FE
    G.钛铁合金,                          -- TI_FE_ALLOY
    G.铌铁,                              -- NB_FE_ALLOY
    G.低碳锰铁,                          -- LOW_C_MN_FE
    G.中碳锰铁,                          -- MID_C_MN_FE
    G.硅碳合金,                          -- SI_C_ALLOY
    G.钢砂铝,                            -- STEEL_AL
    G.高碳铬铁,                          -- HIGH_C_CR_FE
    G.低碳铬铁,                          -- LOW_C_CR_FE
    G.硼铁,                              -- B_FE_ALLOY
    G.钒氮合金,                          -- V_N_ALLOY
    G.镍铜铁,                            -- NI_CU_FE
    G.碳化硅,                            -- SI_CARBIDE
    G.硅钙钡,                            -- SI_CA_BA
    G.硅铝钙,                            -- SI_AL_CA
    G.金属钙线,                          -- CA_WIRE
    G.铝粒,                              -- AL_PARTICLE
    G.铝线,                              -- AL_WIRE
    G.铝线米数,                          -- AL_WIRE_METER
    G.钙铁包芯线,                        -- CA_FE_WIRE
    G.铁钙线,                            -- FE_CA_WIRE
    G.碳线,                              -- C_WIRE
    G.碳包芯线,                          -- C_CORE_WIRE
    G.铝块,                              -- AL_BLOCK
    G.铝棒,                              -- AL_BAR
    G.钢芯铝,                            -- STEEL_AL_CORE
    G.铝钙球,                            -- AL_CA_BALL
    G.挡渣锥,                            -- SLAG_CONE
    G.挡渣塞,                            -- SLAG_PLUG
    G.测温管,                            -- TEMP_TUBE
    G.定氧管,                            -- O2_TUBE
    G.取样器,                            -- SAMPLE_DEVICE
    G.钢筋头,                            -- REBAR_HEAD
    G.新型镁钙球,                        -- NEW_MG_CA_BALL
    G.高碳钛铁,                          -- HIGH_C_TI_FE
    G.新型复合化渣剂,                    -- NEW_SLAG_AGENT
    G.高炉返矿,                          -- BF_RETURN
    G.综合废钢,                          -- COMPREHENSIVE_SCRAP
    G.锰碳合金,                          -- MN_C_ALLOY
    G.小铝锭,                            -- SMALL_AL
    G.高钙活性石灰,                      -- HIGH_CA_LIME_ACTIVE
    G.硅锰合金FeMn55Si13,                -- SI_MN_55_13
    G.窑渣,                              -- KILN_SLAG
    G.高钙复合精炼脱硫剂,                -- HIGH_CA_DESULFUR
    A.STL_WGT,                          -- STL_WGT
    A.REMARK,                           -- REMARK
    M.ZZ_SCRAP_WGT,                     -- ZZ_SCRAP_WGT
    M.ZZ_PI_WGT,                        -- ZZ_PI_WGT
    F.SLAB_WEIGHT,                      -- SLAB_WEIGHT
    Q.RE_TIME,                          -- RE_TIME
    Q.RE_NUM_COM                       -- RE_NUM_COM
FROM SMES_B_BOF_RES A
         LEFT JOIN (SELECT * FROM SMES_B_BOF_IRON_INFO WHERE TO_CHAR(START_TIME, 'YYYYMMDD') >= '20240601') B ON A.IRON_NO = B.IRON_NO
         LEFT JOIN (SELECT * FROM SMES_B_RES_IRON_RECEIVE WHERE TO_CHAR(ARRIVE_TIME, 'YYYYMMDD') >= '20240601') N ON A.IRON_NO = N.IRON_NO
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'C%') C ON A.HEAT_ID = C.HEAT_ID
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') D ON A.HEAT_ID = D.HEAT_ID
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'M%') E ON A.HEAT_ID = E.HEAT_ID
         LEFT JOIN (SELECT * FROM SMES_B_che_steel a WHERE a.SAMPLEID IN (SELECT max(b.SAMPLEID) FROM SMES_B_che_steel b WHERE a.HEAT_ID = b.HEAT_ID AND sampletype like 'L%' AND SAMPLEID like '%A%' GROUP BY SUBSTR(SAMPLETYPE, 0, 1)) AND sampletype like 'L%') J ON A.HEAT_ID = J.HEAT_ID
         LEFT JOIN (SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID, sum(SLAB_WEIGHT) SLAB_WEIGHT FROM smes_b_cutres GROUP BY SUBSTR(HEAT_ID,0,9)) F ON A.HEAT_ID = F.HEAT_ID
         LEFT JOIN (SELECT T.HEAT_ID,
                           SUM(DECODE(T.MAT_CODE, '10201002', T.MAT_WGT, 0)) 硅钛合金,
                           SUM(DECODE(T.MAT_CODE, '10501024', T.MAT_WGT,'10705001', T.MAT_WGT, 0)) 白灰,
                           SUM(DECODE(T.MAT_CODE, '10201010', T.MAT_WGT, 0)) 中碳硅铁,
                           SUM(DECODE(T.MAT_CODE, '10201017', T.MAT_WGT, 0)) 锰铝合金,
                           SUM(DECODE(T.MAT_CODE, '10502003', T.MAT_WGT, 0)) 高效复合助熔剂,
                           SUM(DECODE(T.MAT_CODE, '10501002', T.MAT_WGT, '10705003', T.MAT_WGT, 0)) 轻烧白云石,
                           SUM(DECODE(T.MAT_CODE, '10501015', T.MAT_WGT,'10501029', T.MAT_WGT, 0)) 生白云石,
                           SUM(DECODE(T.MAT_CODE, '10501009', T.MAT_WGT, '10501012', T.MAT_WGT, 0)) 石灰石,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 烧结矿,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 烧结返矿,
                           SUM(DECODE(T.MAT_CODE, '10607001', T.MAT_WGT, 0)) 铁精粉,
                           SUM(DECODE(T.MAT_CODE, '10502004', T.MAT_WGT, 0)) 轻烧镁球,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 高碳镁球,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 高钙灰,
                           SUM(DECODE(T.MAT_CODE, '10502002', T.MAT_WGT, 0)) 精炼预熔渣,
                           SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, 0)) 覆盖剂,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 焦炭,
                           SUM(DECODE(T.MAT_CODE, '10502009', T.MAT_WGT, 0)) 萤石球,
                           SUM(DECODE(T.MAT_CODE, '10201001', T.MAT_WGT, 0)) 硅铁,
                           SUM(DECODE(T.MAT_CODE, '10201006', T.MAT_WGT, 0)) 硅锰合金,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 低铝硅铁,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 高硅硅锰,
                           SUM(DECODE(T.MAT_CODE, '10501033', T.MAT_WGT, 0)) 增碳剂,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 煤质增碳剂,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 低氮增碳剂,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 石油焦增碳剂,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 钒铁,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 氮化钒铁,
                           SUM(DECODE(T.MAT_CODE, '10201004', T.MAT_WGT,'10201005', T.MAT_WGT, 0)) 高碳锰铁,
                           SUM(DECODE(T.MAT_CODE, '10201007', T.MAT_WGT, 0)) 钛铁合金,
                           SUM(DECODE(T.MAT_CODE, '10201016', T.MAT_WGT, 0)) 铌铁,
                           SUM(DECODE(T.MAT_CODE, '10201012', T.MAT_WGT, 0)) 低碳锰铁,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 中碳锰铁,
                           SUM(DECODE(T.MAT_CODE, '10201008', T.MAT_WGT, 0)) 硅碳合金,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 钢砂铝,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 高碳铬铁,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 低碳铬铁,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 硼铁,
                           SUM(DECODE(T.MAT_CODE, '10201003', T.MAT_WGT, 0)) 钒氮合金,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 镍铜铁,
                           SUM(DECODE(T.MAT_CODE, '10202010', T.MAT_WGT, 0)) 碳化硅,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 硅钙钡,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 硅铝钙,
                           SUM(DECODE(T.MAT_CODE, '10202006', T.MAT_WGT, 0)) 金属钙线,
                           SUM(DECODE(T.MAT_CODE, '10202003', T.MAT_WGT, 0)) 铝粒,
                           SUM(DECODE(T.MAT_CODE, '10202002', T.MAT_WGT, 0)) 铝线,
                           SUM(DECODE(T.MAT_CODE, '10202002', T.INS_WGT, 0)) 铝线米数,
                           SUM(DECODE(T.MAT_CODE, '10202009', T.MAT_WGT, 0)) 钙铁包芯线,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 铁钙线,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 碳线,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 碳包芯线,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 铝块,
                           SUM(DECODE(T.MAT_CODE, '', T.MAT_WGT, 0)) 铝棒,
                           SUM(DECODE(T.MAT_CODE, '10202001', T.MAT_WGT, 0)) 钢芯铝,
                           SUM(DECODE(T.MAT_CODE, '10202005', T.MAT_WGT, 0)) 铝钙球,
                           SUM(DECODE(T.MAT_CODE, '10504024', T.MAT_WGT, 0)) 挡渣锥,
                           SUM(DECODE(T.MAT_CODE, '10504021', T.MAT_WGT, 0)) 挡渣塞,
                           SUM(DECODE(T.MAT_CODE, '10504030', T.MAT_WGT, 0)) 测温管,
                           SUM(DECODE(T.MAT_CODE, '10504022', T.MAT_WGT, 0)) 定氧管,
                           SUM(DECODE(T.MAT_CODE, '29801040010001', T.MAT_WGT, '29801040010003', T.MAT_WGT,'29801040010004', T.MAT_WGT,'29801040060001', T.MAT_WGT, '10504029', T.MAT_WGT)) 取样器,
                           SUM(DECODE(T.MAT_CODE, '10401002', T.INS_WGT, 0)) 钢筋头,
                           SUM(DECODE(T.MAT_CODE, '10502005', T.MAT_WGT, 0)) 新型镁钙球,
                           SUM(DECODE(T.MAT_CODE, '10201011', T.MAT_WGT, 0)) 高碳钛铁,
                           SUM(DECODE(T.MAT_CODE, '10502006', T.MAT_WGT, 0)) 新型复合化渣剂,
                           SUM(DECODE(T.MAT_CODE, '10603001', T.MAT_WGT, 0)) 高炉返矿,
                           SUM(DECODE(T.MAT_CODE, '10401014', T.MAT_WGT, 0)) 综合废钢,
                           SUM(DECODE(T.MAT_CODE, '10201020', T.MAT_WGT, 0)) 锰碳合金,
                           SUM(DECODE(T.MAT_CODE, '10202007', T.MAT_WGT, 0)) 小铝锭,
                           SUM(DECODE(T.MAT_CODE, '10501043', T.MAT_WGT, 0)) 高钙活性石灰,
                           SUM(DECODE(T.MAT_CODE, '10201024', T.MAT_WGT, 0)) 硅锰合金FeMn55Si13,
                           SUM(DECODE(T.MAT_CODE, '10701002', T.MAT_WGT, 0)) 窑渣,
                           SUM(DECODE(T.MAT_CODE, '10503005', T.MAT_WGT, 0)) 高钙复合精炼脱硫剂
                    FROM SMES_B_MAT_USE t WHERE T.wp_code = '2C01' GROUP BY T.HEAT_ID) G ON A.HEAT_ID = G.HEAT_ID
         LEFT JOIN (SELECT heat_id, MAX(MAT_QUL_CD) MAT_QUL_CD, max(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM FROM SMES_B_CCMRES
                    WHERE MAC_CODE IN ('2D312','2D313')
                    GROUP BY HEAT_ID) H ON A.HEAT_ID = H.HEAT_ID
         LEFT JOIN (SELECT SEQ_NO, NVL(RECYCLED_STEEL,ZZ_SCRAP_WGT) ZZ_SCRAP_WGT, NVL(SINCE_CYCLE,ZZ_PI_WGT) ZZ_PI_WGT FROM SMES_B_SCRAP_INFO) M ON A.BO_CSMTWO = M.SEQ_NO
         LEFT JOIN SMES_B_MAIN_HEAT P ON A.HEAT_ID = P.HEAT_ID
         LEFT JOIN (SELECT HEAT_ID,MAX(RE_TIME) AS RE_TIME,MAX(RE_NUM_COM) AS RE_NUM_COM FROM SMES_B_ENER_USEC GROUP BY HEAT_ID) Q ON A.HEAT_ID = Q.HEAT_ID
WHERE A.WORK_SHOP = 'L2'
  AND H.STAR_CAST_TIM BETWEEN TO_DATE('2025-08-09 00:00:00' ,'YYYY-MM-DD HH24:MI:SS') AND TO_DATE('2025-08-10 00:00:00' ,'YYYY-MM-DD HH24:MI:SS');
INSERT INTO CARBON_LGMS_L1_CCM_FP_REPORTER (
    HEAT_ID, CAST_ID, WORK_SHOP, P_DATE,
    GROUP1, CAST_SDL, HEAT_SDL, SHIFT, CAST_SEQ_N, <PERSON><PERSON><PERSON><PERSON><PERSON>, STL_WGT_WAIT,
    STL_GRD_CD, STL_COD, LADLE_ID, AUTO_OPEN_FLG,
    AVG_CAST_SPEED, TARGET_ARR_TMP, LADLE_ARR_TMP,
    LADLE_ARR_TIME, STAR_CAST_TIM, END_CAST_TIM,
    LD_ARR_NET_WGHT, LD_DEP_NET_WGHT, CAST_WGT, HEAT_SLAB_CNT,
    TD_NO, TUNDISH_TEMP_AVG1, TUNDISH_TEMP_AVG2, TUNDISH_TEMP_AVG3,
    CASTING_POWDER_TYPE, COVERING_AGENT_TYPE, MAC_CODE,
    SLAB_CNT, SLAB_WGT, MEMO,
    CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA, CCM_TI, CCM_CU, CCM_NI, CCM_B, CCM_N, CCM_CEQ,
    PART_ONE, PART_TWO, PART_THREE, PART_FOUR, PART_FIVE, PART_SIX, PART_SEVEN, PART_EIGET,
    SPEED_ONE, SPEED_TWO, SPEED_THREE, SPEED_FOUR, SPEED_FIVE, SPEED_SIX, SPEED_SEVEN, SPEED_EIGET,
    MOTEMP_ONE, MOTEMP_TWO, MOTEMP_THREE, MOTEMP_FOUR, MOTEMP_FIVE, MOTEMP_SIX, MOTEMP_SEVEN, MOTEMP_EIGET,
    MODIF_ONE, MODIF_TWO, MODIF_THREE, MODIF_FOUR, MODIF_FIVE, MODIF_SIX, MODIF_SEVEN, MODIF_EIGET,
    MOSURE_ONE, MOSURE_TWO, MOSURE_THREE, MOSURE_FOUR, MOSURE_FIVE, MOSURE_SIX, MOSURE_SEVEN, MOSURE_EIGET,
    STRAND_NO1_BHZ, STRAND_NO2_BHZ, STRAND_NO1_FGJ, STRAND_NO2_FGJ
)
SELECT
    C.HEAT_ID,                          -- 炉次号
    C.CAST_ID,                          -- 连浇号
    'L1',                               -- 车间代码
    C.CCM_DATE,                         -- 作业日期
    C.GROUP1,                           -- 班组
    C.CAST_SDL,                         -- 浇次SDL
    C.HEAT_SDL,                         -- 炉次SDL
    C.SHIFT,                            -- 班次
    C.CAST_SEQ_N,                       -- 浇次序号
    C.PENSHUI,                          -- 喷水
    C.STL_WGT_WAIT,                     -- 钢水等待重量
    C.STL_GRD_CD,                       -- 钢种
    C.STL_COD,                          -- 钢种代码
    C.LADLE_ID,                         -- 钢包号
    C.AUTO_OPEN_FLG,                    -- 自开方式
    C.AVG_CAST_SPEED,                   -- 平均拉速
    C.TARGET_ARR_TMP,                   -- 要钢温度
    C.LADLE_ARR_TMP,                    -- 到达温度
    C.LADLE_ARR_TIME,                   -- 包到时间
    C.STAR_CAST_TIM,                    -- 开始浇铸时间
    C.END_CAST_TIM,                     -- 结束浇铸时间
    C.LD_ARR_NET_WGHT,                  -- 开浇重量
    C.LD_DEP_NET_WGHT,                  -- 终浇重量
    C.CAST_WGT,                         -- 浇铸重量
    C.HEAT_SLAB_CNT,                    -- 炉次钢坯数量
    C.TD_NO,                            -- 中包号
    C.TUNDISH_TEMP_AVG1,                -- 中间罐平均温度1
    C.TUNDISH_TEMP_AVG2,                -- 中间罐平均温度2
    C.TUNDISH_TEMP_AVG3,                -- 中间罐平均温度3
    C.CASTING_POWDER_TYPE,              -- 保护渣类别
    C.COVERING_AGENT_TYPE,              -- 覆盖剂类别
    C.MAC_CODE,                         -- 座次号
    D.SLAB_CNT,                         -- 支数
    D.SLAB_WGT,                         -- 重量
    C.MEMO,                             -- 备注
    B.C,                                -- 碳含量
    B.Mn,                               -- 锰含量
    B.Si,                               -- 硅含量
    B.S,                                -- 硫含量
    B.P,                                -- 磷含量
    B.V,                                -- 钒含量
    B.ALS,                              -- ALS含量
    B.AL,                               -- ALT含量
    B.CA,                               -- 钙含量
    B.TI,                               -- 钛含量
    B.CU,                               -- 铜含量
    B.NI,                               -- 镍含量
    B.B,                                -- 硼含量
    B.N,                                -- 氮含量
    B.CEQ,                              -- CEQ含量
    AA.PART_ONE,                        -- 第1流结晶器编号
    AA.PART_TWO,                        -- 第2流结晶器编号
    AA.PART_THREE,                      -- 第3流结晶器编号
    AA.PART_FOUR,                       -- 第4流结晶器编号
    AA.PART_FIVE,                       -- 第5流结晶器编号
    AA.PART_SIX,                        -- 第6流结晶器编号
    AA.PART_SEVEN,                      -- 第7流结晶器编号
    AA.PART_EIGET,                      -- 第8流结晶器编号
    AA.SPEED_ONE,                       -- 第1流速度
    AA.SPEED_TWO,                       -- 第2流速度
    AA.SPEED_THREE,                     -- 第3流速度
    AA.SPEED_FOUR,                      -- 第4流速度
    AA.SPEED_FIVE,                      -- 第5流速度
    AA.SPEED_SIX,                       -- 第6流速度
    AA.SPEED_SEVEN,                     -- 第7流速度
    AA.SPEED_EIGET,                     -- 第8流速度
    BB.MOTEMP_ONE,                      -- 第1流结晶器温度
    BB.MOTEMP_TWO,                      -- 第2流结晶器温度
    BB.MOTEMP_THREE,                    -- 第3流结晶器温度
    BB.MOTEMP_FOUR,                     -- 第4流结晶器温度
    BB.MOTEMP_FIVE,                     -- 第5流结晶器温度
    BB.MOTEMP_SIX,                      -- 第6流结晶器温度
    BB.MOTEMP_SEVEN,                    -- 第7流结晶器温度
    BB.MOTEMP_EIGET,                    -- 第8流结晶器温度
    BB.MODIF_ONE,                       -- 第1流结晶器水温差
    BB.MODIF_TWO,                       -- 第2流结晶器水温差
    BB.MODIF_THREE,                     -- 第3流结晶器水温差
    BB.MODIF_FOUR,                      -- 第4流结晶器水温差
    BB.MODIF_FIVE,                      -- 第5流结晶器水温差
    BB.MODIF_SIX,                       -- 第6流结晶器水温差
    BB.MODIF_SEVEN,                     -- 第7流结晶器水温差
    BB.MODIF_EIGET,                     -- 第8流结晶器水温差
    BB.MOSURE_ONE,                      -- 第1流结晶器水温度差
    BB.MOSURE_TWO,                      -- 第2流结晶器水温度差
    BB.MOSURE_THREE,                    -- 第3流结晶器水温度差
    BB.MOSURE_FOUR,                     -- 第4流结晶器水温度差
    BB.MOSURE_FIVE,                     -- 第5流结晶器水温度差
    BB.MOSURE_SIX,                      -- 第6流结晶器水温度差
    BB.MOSURE_SEVEN,                    -- 第7流结晶器水温度差
    BB.MOSURE_EIGET,                    -- 第8流结晶器水温度差
    GG.STRAND_NO1_BHZ,                  -- 第1流保护渣
    GG.STRAND_NO2_BHZ,                  -- 第2流保护渣
    GG.STRAND_NO1_FGJ,                  -- 第1流覆盖剂
    GG.STRAND_NO2_FGJ                   -- 第2流覆盖剂
FROM SMES_B_CCMRES C,
     (SELECT SUBSTR(HEAT_ID,0,9) HEAT_ID,
             TREAT_NO,
             COUNT(1) SLAB_CNT,
             SUM(SLAB_WEIGHT) SLAB_WGT
      FROM SMES_B_CUTRES
      GROUP BY SUBSTR(HEAT_ID,0,9), TREAT_NO) D,
     (SELECT *
      FROM SMES_B_che_steel a
      WHERE a.SEQ_NO IN (SELECT MAX(b.SEQ_NO)
                         FROM SMES_B_che_steel b
                         WHERE a.HEAT_ID = b.HEAT_ID
                         GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
        AND sampletype LIKE 'C%') B,
     (SELECT HEAT_ID,
             TREAT_NO,
             MAX(DECODE(STRAND_NO, '1', MOULD_NO)) PART_ONE,
             MAX(DECODE(STRAND_NO, '2', MOULD_NO)) PART_TWO,
             MAX(DECODE(STRAND_NO, '3', MOULD_NO)) PART_THREE,
             MAX(DECODE(STRAND_NO, '4', MOULD_NO)) PART_FOUR,
             MAX(DECODE(STRAND_NO, '5', MOULD_NO)) PART_FIVE,
             MAX(DECODE(STRAND_NO, '6', MOULD_NO)) PART_SIX,
             MAX(DECODE(STRAND_NO, '7', MOULD_NO)) PART_SEVEN,
             MAX(DECODE(STRAND_NO, '8', MOULD_NO)) PART_EIGET,
             MAX(DECODE(STRAND_NO, '1', SPEEDVALUE)) SPEED_ONE,
             MAX(DECODE(STRAND_NO, '2', SPEEDVALUE)) SPEED_TWO,
             MAX(DECODE(STRAND_NO, '3', SPEEDVALUE)) SPEED_THREE,
             MAX(DECODE(STRAND_NO, '4', SPEEDVALUE)) SPEED_FOUR,
             MAX(DECODE(STRAND_NO, '5', SPEEDVALUE)) SPEED_FIVE,
             MAX(DECODE(STRAND_NO, '6', SPEEDVALUE)) SPEED_SIX,
             MAX(DECODE(STRAND_NO, '7', SPEEDVALUE)) SPEED_SEVEN,
             MAX(DECODE(STRAND_NO, '8', SPEEDVALUE)) SPEED_EIGET
      FROM SMES_B_CCMRES_STREAM
      GROUP BY HEAT_ID, TREAT_NO) AA,
     (SELECT HEAT_ID,
             TREAT_NO,
             MAX(DECODE(STRAND_NO, '1', MOULD_ACTVALUE)) MOTEMP_ONE,
             MAX(DECODE(STRAND_NO, '2', MOULD_ACTVALUE)) MOTEMP_TWO,
             MAX(DECODE(STRAND_NO, '3', MOULD_ACTVALUE)) MOTEMP_THREE,
             MAX(DECODE(STRAND_NO, '4', MOULD_ACTVALUE)) MOTEMP_FOUR,
             MAX(DECODE(STRAND_NO, '5', MOULD_ACTVALUE)) MOTEMP_FIVE,
             MAX(DECODE(STRAND_NO, '6', MOULD_ACTVALUE)) MOTEMP_SIX,
             MAX(DECODE(STRAND_NO, '7', MOULD_ACTVALUE)) MOTEMP_SEVEN,
             MAX(DECODE(STRAND_NO, '8', MOULD_ACTVALUE)) MOTEMP_EIGET,
             MAX(DECODE(STRAND_NO, '1', MLD_WAT_T)) MODIF_ONE,
             MAX(DECODE(STRAND_NO, '2', MLD_WAT_T)) MODIF_TWO,
             MAX(DECODE(STRAND_NO, '3', MLD_WAT_T)) MODIF_THREE,
             MAX(DECODE(STRAND_NO, '4', MLD_WAT_T)) MODIF_FOUR,
             MAX(DECODE(STRAND_NO, '5', MLD_WAT_T)) MODIF_FIVE,
             MAX(DECODE(STRAND_NO, '6', MLD_WAT_T)) MODIF_SIX,
             MAX(DECODE(STRAND_NO, '7', MLD_WAT_T)) MODIF_SEVEN,
             MAX(DECODE(STRAND_NO, '8', MLD_WAT_T)) MODIF_EIGET,
             MAX(DECODE(STRAND_NO, '1', MOULDWATERDELTATEMP)) MOSURE_ONE,
             MAX(DECODE(STRAND_NO, '2', MOULDWATERDELTATEMP)) MOSURE_TWO,
             MAX(DECODE(STRAND_NO, '3', MOULDWATERDELTATEMP)) MOSURE_THREE,
             MAX(DECODE(STRAND_NO, '4', MOULDWATERDELTATEMP)) MOSURE_FOUR,
             MAX(DECODE(STRAND_NO, '5', MOULDWATERDELTATEMP)) MOSURE_FIVE,
             MAX(DECODE(STRAND_NO, '6', MOULDWATERDELTATEMP)) MOSURE_SIX,
             MAX(DECODE(STRAND_NO, '7', MOULDWATERDELTATEMP)) MOSURE_SEVEN,
             MAX(DECODE(STRAND_NO, '8', MOULDWATERDELTATEMP)) MOSURE_EIGET
      FROM SMES_B_CCM_PAR
      GROUP BY HEAT_ID, TREAT_NO) BB,
     (SELECT *
      FROM (SELECT *
            FROM (SELECT t.heat_id,
                         t.treat_no,
                         t.strand_no,
                         DECODE(t.bhz_seq, bb.seq_no, bb.part_id, '') bhz,
                         DECODE(t.fgj_seq, cc.seq_no, cc.part_id, '') fgj
                  FROM SMES_B_CCMRES_STREAM t,
                       (SELECT *
                        FROM SMES_C_PARTRES
                        WHERE PART_CODE LIKE '%02') bb,
                       (SELECT *
                        FROM SMES_C_PARTRES
                        WHERE PART_CODE LIKE '%03') cc
                  WHERE t.bhz_seq = bb.seq_no(+)
                    AND t.fgj_seq = cc.seq_no(+)) AAA)
          PIVOT(MAX(BHZ) BHZ, MAX(FGJ) FGJ
          FOR strand_no IN('1' AS strand_no1,
              '2' AS strand_no2,
              '3' AS strand_no3,
              '4' AS strand_no4,
              '5' AS strand_no5,
              '6' AS strand_no6,
              '7' AS strand_no7,
              '8' AS strand_no8))) GG
WHERE C.HEAT_ID = D.HEAT_ID(+)
  AND C.TREAT_NO = D.TREAT_NO(+)
  AND C.HEAT_ID = AA.HEAT_ID(+)
  AND C.TREAT_NO = AA.TREAT_NO(+)
  AND C.HEAT_ID = BB.HEAT_ID(+)
  AND C.TREAT_NO = BB.TREAT_NO(+)
  AND C.HEAT_ID = B.HEAT_ID(+)
  AND C.HEAT_ID = GG.HEAT_ID(+)
  AND C.TREAT_NO = GG.TREAT_NO(+)
  AND C.WORK_SHOP = 'L1'
  AND TO_CHAR(C.STAR_CAST_TIM, 'YYYY-MM-DD') BETWEEN '2025-08-09' AND '2025-08-09'
  and C.MAC_CODE in ('1D311','1D313','1D314') AND  C.HEAT_ID NOT LIKE 'L5%'
ORDER BY C.STAR_CAST_TIM ASC;
-- 炼钢日报表数据插入SQL
-- 将一钢(L1)和二钢(L2)的日报数据插入到DAILY_STEEL_REPORT表中
-- 包含当日和累计的各项指标计算
-- 创建时间: 2025-08-09

-- 插入一钢(L1)日报数据
INSERT INTO CARBON_LGMS_DAILY_REPORTER (
    REPORT_DATE, UNIT_CODE, UNIT_NAME,
    DAILY_FURNACE_COUNT, DAILY_SLAB_OUTPUT, DAILY_IRON_CONSUMPTION, DAILY_SCRAP_CONSUMPTION,
    DAILY_IRON_UNIT_CONSUMPTION, DAILY_SCRAP_UNIT_CONSUMPTION, DAILY_STEEL_IRON_CONSUMPTION,
    DAILY_LIME_UNIT_CONSUMPTION, DAILY_DOLOMITE_UNIT_CONSUMPTION, DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
    DAILY_SIMN_UNIT_CONSUMPTION, DAILY_SIFE_UNIT_CONSUMPTION, DAILY_KILN_SLAG_UNIT_CONSUMPTION,
    CUMULATIVE_FURNACE_COUNT, CUMULATIVE_SLAB_OUTPUT, CUMULATIVE_IRON_CONSUMPTION, CUMULATIVE_SCRAP_CONSUMPTION,
    CUMULATIVE_IRON_UNIT_CONSUMPTION, CUMULATIVE_SCRAP_UNIT_CONSUMPTION, CUMULATIVE_STEEL_IRON_CONSUMPTION,
    CUMULATIVE_LIME_UNIT_CONSUMPTION, CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION, CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
    CUMULATIVE_SIMN_UNIT_CONSUMPTION, CUMULATIVE_SIFE_UNIT_CONSUMPTION, CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
)
WITH
-- 1. 一钢产量（日）- 使用第二个SQL的计算方式
daily_prod_l1_cte AS (
    SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_D
    FROM SMES_B_BOF_RES A
             LEFT JOIN (
        SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
        FROM smes_b_cutres
        GROUP BY SUBSTR(HEAT_ID, 0, 9)
    ) F ON A.HEAT_ID = F.HEAT_ID
             LEFT JOIN (
        SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
        FROM SMES_B_CCMRES
        WHERE MAC_CODE IN ('1D311', '1D313', '1D314')
        GROUP BY HEAT_ID
    ) H ON A.HEAT_ID = H.HEAT_ID
    WHERE A.WORK_SHOP = 'L1'
      AND A.MAC_CODE != '1C015'
      AND H.STAR_CAST_TIM BETWEEN TO_DATE('2025-08-09 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE('2025-08-10 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
),

-- 2. 一钢产量（月累计）- 使用第二个SQL的计算方式，但修改日期范围为月累计
monthly_prod_l1_cte AS (
    SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_M
    FROM SMES_B_BOF_RES A
             LEFT JOIN (
        SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
        FROM smes_b_cutres
        GROUP BY SUBSTR(HEAT_ID, 0, 9)
    ) F ON A.HEAT_ID = F.HEAT_ID
             LEFT JOIN (
        SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
        FROM SMES_B_CCMRES
        WHERE MAC_CODE IN ('1D311', '1D313', '1D314')
        GROUP BY HEAT_ID
    ) H ON A.HEAT_ID = H.HEAT_ID
    WHERE A.WORK_SHOP = 'L1'
      AND A.MAC_CODE != '1C015'
      AND H.STAR_CAST_TIM BETWEEN TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
        AND TO_DATE('2025-08-10 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
),

-- 3. 一钢废钢盘库量（日）
daily_inventory_l1_cte
    AS (SELECT NVL(SUM(DECODE(MAT_CODE, '10703001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)),
                   0)                                                                                           AS PTTSPKL_D,
               NVL(SUM(DECODE(MAT_CODE, '10703002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)),
                   0)                                                                                           AS QMTSPKL_D,
               NVL(SUM(DECODE(MAT_CODE,
                              '10401014', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10401015', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10401002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10606002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10609003', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10609006', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10402001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10609012', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10704001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              0
                       )),
                   0)                                                                                           AS FGPKL_D
        FROM SMES_F_MAKE_WAREHOUSE
        WHERE WORK_SHOP = 'L1'
          AND TRUNC(DT_TIME, 'DD') = TO_DATE('2025-08-09', 'YYYY-MM-DD')),
-- 4. 一钢废钢盘库量（月累计）
monthly_inventory_l1_cte
    AS (SELECT NVL(SUM(DECODE(MAT_CODE, '10703001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)),
                   0)                                                                                           AS PTTSPKL_M,
               NVL(SUM(DECODE(MAT_CODE, '10703002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)),
                   0)                                                                                           AS QMTSPKL_M,
               NVL(SUM(DECODE(MAT_CODE,
                              '10401014', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10401015', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10401002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10606002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10609003', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10609006', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10402001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10609012', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              '10704001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                              0
                       )),
                   0)                                                                                           AS FGPKL_M
        FROM SMES_F_MAKE_WAREHOUSE
        WHERE WORK_SHOP = 'L1'
          AND DT_TIME >= TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
          AND DT_TIME < TO_DATE('2025-08-09', 'YYYY-MM-DD') + 1),
-- 5. 一钢日消耗数据
daily_consumption_l1_cte AS (SELECT NVL(SUM(CC.IRON_WGT), 0) AS IRON_WGT_D,
                                    NVL(SUM(DD.FG), 0)       AS TOTAL_SCRAP_D,
                                    NVL(SUM(DD.BAIHUI), 0)   AS BAIHUI_D,
                                    NVL(SUM(DD.QSBYS), 0)    AS QSBYS_D,
                                    NVL(SUM(DD.BYS), 0)      AS BYS_D,
                                    NVL(SUM(DD.SHS), 0)      AS SHS_D,
                                    NVL(SUM(DD.GMHJ), 0)     AS GMHJ_D,
                                    NVL(SUM(DD.GUITIE), 0)   AS GUITIE_D,
                                    NVL(SUM(DD.YZ), 0)       AS YZ_D
                             FROM SMES_B_CCMRES AA
                                      LEFT JOIN
                                  SMES_B_BOF_RES CC ON AA.HEAT_ID = CC.HEAT_ID
                                      LEFT JOIN
                                  (SELECT HEAT_ID,
                                          SUM(DECODE(MAT_CODE, '10401014', MAT_WGT / 1000, '10401015', MAT_WGT / 1000,
                                                     '10704001', MAT_WGT / 1000, 0))                         AS FG,
                                          SUM(DECODE(MAT_CODE, '10201001', MAT_WGT, 0))                      AS GUITIE,
                                          SUM(DECODE(MAT_CODE, '10201006', MAT_WGT, 0))                      AS GMHJ,
                                          SUM(DECODE(MAT_CODE, '10501024', MAT_WGT, '10705001', MAT_WGT, 0)) AS BAIHUI,
                                          SUM(DECODE(MAT_CODE, '10501002', MAT_WGT, '10705003', MAT_WGT, 0)) AS QSBYS,
                                          SUM(DECODE(MAT_CODE, '10501015', MAT_WGT, '10501029', MAT_WGT, 0)) AS BYS,
                                          SUM(DECODE(MAT_CODE, '10501035', MAT_WGT, '10501036', MAT_WGT, '10501012',
                                                     MAT_WGT, 0))                                            AS SHS,
                                          SUM(DECODE(MAT_CODE, '10701002', MAT_WGT, 0))                      AS YZ
                                   FROM SMES_B_MAT_USE
                                   GROUP BY HEAT_ID) DD ON AA.HEAT_ID = DD.HEAT_ID
                             WHERE AA.WORK_SHOP = 'L1'
                               AND TRUNC(AA.STAR_CAST_TIM, 'DD') = TO_DATE('2025-08-09', 'YYYY-MM-DD')),
-- 6. 一钢月消耗数据
monthly_consumption_l1_cte AS (SELECT NVL(SUM(CC.IRON_WGT), 0) AS IRON_WGT_M,
                                      NVL(SUM(DD.FG), 0)       AS TOTAL_SCRAP_M,
                                      NVL(SUM(DD.BAIHUI), 0)   AS BAIHUI_M,
                                      NVL(SUM(DD.QSBYS), 0)    AS QSBYS_M,
                                      NVL(SUM(DD.BYS), 0)      AS BYS_M,
                                      NVL(SUM(DD.SHS), 0)      AS SHS_M,
                                      NVL(SUM(DD.GMHJ), 0)     AS GMHJ_M,
                                      NVL(SUM(DD.GUITIE), 0)   AS GUITIE_M,
                                      NVL(SUM(DD.YZ), 0)       AS YZ_M
                               FROM SMES_B_CCMRES AA
                                        LEFT JOIN
                                    SMES_B_BOF_RES CC ON AA.HEAT_ID = CC.HEAT_ID
                                        LEFT JOIN
                                    (SELECT HEAT_ID,
                                            SUM(DECODE(MAT_CODE, '10401014', MAT_WGT / 1000, '10401015', MAT_WGT / 1000,
                                                       '10704001', MAT_WGT / 1000, 0))                         AS FG,
                                            SUM(DECODE(MAT_CODE, '10201001', MAT_WGT, 0))                      AS GUITIE,
                                            SUM(DECODE(MAT_CODE, '10201006', MAT_WGT, 0))                      AS GMHJ,
                                            SUM(DECODE(MAT_CODE, '10501024', MAT_WGT, '10705001', MAT_WGT, 0)) AS BAIHUI,
                                            SUM(DECODE(MAT_CODE, '10501002', MAT_WGT, '10705003', MAT_WGT, 0)) AS QSBYS,
                                            SUM(DECODE(MAT_CODE, '10501015', MAT_WGT, '10501029', MAT_WGT, 0)) AS BYS,
                                            SUM(DECODE(MAT_CODE, '10501035', MAT_WGT, '10501036', MAT_WGT, '10501012',
                                                       MAT_WGT, 0))                                            AS SHS,
                                            SUM(DECODE(MAT_CODE, '10701002', MAT_WGT, 0))                      AS YZ
                                     FROM SMES_B_MAT_USE
                                     GROUP BY HEAT_ID) DD ON AA.HEAT_ID = DD.HEAT_ID
                               WHERE AA.WORK_SHOP = 'L1'
                                 AND AA.STAR_CAST_TIM >= TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
                                 AND AA.STAR_CAST_TIM < TO_DATE('2025-08-09', 'YYYY-MM-DD') + 1),
-- 7. 一钢炉数（日）
daily_furnace_l1_cte AS (SELECT COUNT(*) AS FURNACE_COUNT_D
                         FROM SMES_B_BOF_RES bof
                         WHERE bof.WORK_SHOP = 'L1'
                           AND bof.MAC_CODE IN ('1C013', '1C014')
                           AND EXISTS (SELECT 1
                                       FROM SMES_B_CCMRES ccm
                                       WHERE ccm.heat_id = bof.HEAT_ID
                                         AND ccm.WORK_SHOP = 'L1'
                                         AND TRUNC(ccm.star_cast_tim, 'DD') = TO_DATE('2025-08-09', 'YYYY-MM-DD')
                                         AND ccm.MAC_CODE in ('1D311','1D313','1D314')
                         )
),
-- 8. 一钢炉数（月累计）
monthly_furnace_l1_cte AS (SELECT COUNT(*) AS FURNACE_COUNT_M
                           FROM SMES_B_BOF_RES bof
                           WHERE bof.WORK_SHOP = 'L1'
                             AND bof.MAC_CODE IN ('1C013', '1C014')
                             AND EXISTS (SELECT 1
                                         FROM SMES_B_CCMRES ccm
                                         WHERE ccm.heat_id = bof.HEAT_ID
                                           AND ccm.WORK_SHOP = 'L1'
                                           AND ccm.star_cast_tim >= TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
                                           AND ccm.star_cast_tim < TO_DATE('2025-08-09', 'YYYY-MM-DD') + 1
                                           AND ccm.MAC_CODE in ('1D311','1D313','1D314')
                           )
)
-- 最终一钢数据查询
SELECT TO_DATE('2025-08-09', 'YYYY-MM-DD'),                                              -- REPORT_DATE
       'L1',                                                                             -- UNIT_CODE
       '炼钢一厂',                                                                       -- UNIT_NAME

       -- 当日数据
       d_furnace.FURNACE_COUNT_D,                                                        -- DAILY_FURNACE_COUNT
       d_prod.SLAB_WGT_D,                                                                -- DAILY_SLAB_OUTPUT
       (d_con.IRON_WGT_D + d_inv.PTTSPKL_D + d_inv.QMTSPKL_D),                           -- DAILY_IRON_CONSUMPTION
       (d_con.TOTAL_SCRAP_D + d_inv.FGPKL_D),                                            -- DAILY_SCRAP_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.IRON_WGT_D + d_inv.PTTSPKL_D + d_inv.QMTSPKL_D) * 1000 /
                                           d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_IRON_UNIT_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.TOTAL_SCRAP_D + d_inv.FGPKL_D) * 1000 / d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_SCRAP_UNIT_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN
               (d_con.IRON_WGT_D + d_inv.PTTSPKL_D + d_inv.QMTSPKL_D + d_con.TOTAL_SCRAP_D + d_inv.FGPKL_D) * 1000 /
               d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_STEEL_IRON_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BAIHUI_D / d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_LIME_UNIT_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BYS_D / d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_DOLOMITE_UNIT_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.QSBYS_D / d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION
       CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GMHJ_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_SIMN_UNIT_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GUITIE_D / d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_SIFE_UNIT_CONSUMPTION
       CASE
           WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.YZ_D / d_prod.SLAB_WGT_D
           ELSE 0 END,                                                                   -- DAILY_KILN_SLAG_UNIT_CONSUMPTION

       -- 累计数据
       m_furnace.FURNACE_COUNT_M,                                                        -- CUMULATIVE_FURNACE_COUNT
       m_prod.SLAB_WGT_M,                                                                -- CUMULATIVE_SLAB_OUTPUT
       (m_con.IRON_WGT_M + m_inv.PTTSPKL_M + m_inv.QMTSPKL_M),                           -- CUMULATIVE_IRON_CONSUMPTION
       (m_con.TOTAL_SCRAP_M + m_inv.FGPKL_M),                                            -- CUMULATIVE_SCRAP_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN (m_con.IRON_WGT_M + m_inv.PTTSPKL_M + m_inv.QMTSPKL_M) * 1000 /
                                           m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_IRON_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN (m_con.TOTAL_SCRAP_M + m_inv.FGPKL_M) * 1000 / m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_SCRAP_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN
               (m_con.IRON_WGT_M + m_inv.PTTSPKL_M + m_inv.QMTSPKL_M + m_con.TOTAL_SCRAP_M + m_inv.FGPKL_M) * 1000 /
               m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_STEEL_IRON_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.BAIHUI_M / m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_LIME_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.BYS_M / m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.QSBYS_M / m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.GMHJ_M / m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_SIMN_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.GUITIE_M / m_prod.SLAB_WGT_M
           ELSE 0 END,                                                                   -- CUMULATIVE_SIFE_UNIT_CONSUMPTION
       CASE
           WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.YZ_M / m_prod.SLAB_WGT_M
           ELSE 0 END                                                                    -- CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
FROM daily_prod_l1_cte d_prod,
     monthly_prod_l1_cte m_prod,
     daily_inventory_l1_cte d_inv,
     monthly_inventory_l1_cte m_inv,
     daily_consumption_l1_cte d_con,
     monthly_consumption_l1_cte m_con,
     daily_furnace_l1_cte d_furnace,
     monthly_furnace_l1_cte m_furnace;


-- 插入二钢(L2)日报数据
INSERT INTO CARBON_LGMS_DAILY_REPORTER (
    REPORT_DATE, UNIT_CODE, UNIT_NAME,
    DAILY_FURNACE_COUNT, DAILY_SLAB_OUTPUT, DAILY_IRON_CONSUMPTION, DAILY_SCRAP_CONSUMPTION,
    DAILY_IRON_UNIT_CONSUMPTION, DAILY_SCRAP_UNIT_CONSUMPTION, DAILY_STEEL_IRON_CONSUMPTION,
    DAILY_LIME_UNIT_CONSUMPTION, DAILY_DOLOMITE_UNIT_CONSUMPTION, DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
    DAILY_SIMN_UNIT_CONSUMPTION, DAILY_SIFE_UNIT_CONSUMPTION, DAILY_KILN_SLAG_UNIT_CONSUMPTION,
    CUMULATIVE_FURNACE_COUNT, CUMULATIVE_SLAB_OUTPUT, CUMULATIVE_IRON_CONSUMPTION, CUMULATIVE_SCRAP_CONSUMPTION,
    CUMULATIVE_IRON_UNIT_CONSUMPTION, CUMULATIVE_SCRAP_UNIT_CONSUMPTION, CUMULATIVE_STEEL_IRON_CONSUMPTION,
    CUMULATIVE_LIME_UNIT_CONSUMPTION, CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION, CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION,
    CUMULATIVE_SIMN_UNIT_CONSUMPTION, CUMULATIVE_SIFE_UNIT_CONSUMPTION, CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
)
WITH
-- 1. 二钢产量（日）- 使用第二个SQL的计算方式
daily_prod_l2_cte AS (
    SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_D
    FROM SMES_B_BOF_RES A
             LEFT JOIN (
        SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
        FROM smes_b_cutres
        GROUP BY SUBSTR(HEAT_ID, 0, 9)
    ) F ON A.HEAT_ID = F.HEAT_ID
             LEFT JOIN (
        SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
        FROM SMES_B_CCMRES
        WHERE MAC_CODE IN ('2D312','2D313')
        GROUP BY HEAT_ID
    ) H ON A.HEAT_ID = H.HEAT_ID
    WHERE A.WORK_SHOP = 'L2'
      AND H.STAR_CAST_TIM BETWEEN TO_DATE('2025-08-09 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE('2025-08-10 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
),

-- 2. 二钢产量（月累计）- 使用第二个SQL的计算方式，但修改日期范围为月累计
monthly_prod_l2_cte AS (
    SELECT NVL(SUM(F.SLAB_WEIGHT), 0) AS SLAB_WGT_M
    FROM SMES_B_BOF_RES A
             LEFT JOIN (
        SELECT SUBSTR(HEAT_ID, 0, 9) HEAT_ID, SUM(SLAB_WEIGHT) SLAB_WEIGHT
        FROM smes_b_cutres
        GROUP BY SUBSTR(HEAT_ID, 0, 9)
    ) F ON A.HEAT_ID = F.HEAT_ID
             LEFT JOIN (
        SELECT heat_id, MAX(PLAN_HEAT_ID) PLAN_HEAT_ID, MAX(STAR_CAST_TIM) STAR_CAST_TIM
        FROM SMES_B_CCMRES
        WHERE MAC_CODE IN ('2D312','2D313')
        GROUP BY HEAT_ID
    ) H ON A.HEAT_ID = H.HEAT_ID
    WHERE A.WORK_SHOP = 'L2'
      AND H.STAR_CAST_TIM BETWEEN TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
        AND TO_DATE('2025-08-10 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
),

-- 3. 二钢废钢盘库量（日）
daily_inventory_l2_cte AS (
    SELECT
        NVL(SUM(DECODE(MAT_CODE, '10703001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)), 0) AS PTTSPKL_D,
        NVL(SUM(DECODE(MAT_CODE, '10703002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)), 0) AS QMTSPKL_D,
        NVL(SUM(DECODE(MAT_CODE,
                       '10401014', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10401015', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10401002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10606002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10609003', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10609006', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10402001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10609012', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10704001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       0
                )), 0) AS FGPKL_D
    FROM
        SMES_F_MAKE_WAREHOUSE
    WHERE
        WORK_SHOP = 'L2'
      AND TRUNC(DT_TIME, 'DD') = TO_DATE('2025-08-09', 'YYYY-MM-DD')
),
-- 4. 二钢废钢盘库量（月累计）
monthly_inventory_l2_cte AS (
    SELECT
        NVL(SUM(DECODE(MAT_CODE, '10703001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)), 0) AS PTTSPKL_M,
        NVL(SUM(DECODE(MAT_CODE, '10703002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0), 0)), 0) AS QMTSPKL_M,
        NVL(SUM(DECODE(MAT_CODE,
                       '10401014', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10401015', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10401002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10606002', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10609003', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10609006', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10402001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10609012', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       '10704001', NVL(INVENTORY_AFTER, 0) - NVL(INVENTORY_BEFORE, 0),
                       0
                )), 0) AS FGPKL_M
    FROM
        SMES_F_MAKE_WAREHOUSE
    WHERE
        WORK_SHOP = 'L2'
      AND DT_TIME >= TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
      AND DT_TIME < TO_DATE('2025-08-09', 'YYYY-MM-DD') + 1
),
-- 5. 二钢日消耗数据
daily_consumption_l2_cte AS (
    SELECT
        NVL(SUM(CC.IRON_WGT), 0) AS IRON_WGT_D,
        NVL(SUM(DD.FG), 0) AS TOTAL_SCRAP_D,
        NVL(SUM(DD.BAIHUI), 0) AS BAIHUI_D,
        NVL(SUM(DD.QSBYS), 0) AS QSBYS_D,
        NVL(SUM(DD.BYS), 0) AS BYS_D,
        NVL(SUM(DD.SHS), 0) AS SHS_D,
        NVL(SUM(DD.GMHJ), 0) AS GMHJ_D,
        NVL(SUM(DD.GUITIE), 0) AS GUITIE_D,
        NVL(SUM(DD.YZ), 0) AS YZ_D
    FROM
        SMES_B_CCMRES AA
            LEFT JOIN
        SMES_B_BOF_RES CC ON AA.HEAT_ID = CC.HEAT_ID
            LEFT JOIN
        (
            SELECT
                HEAT_ID,
                SUM(DECODE(MAT_CODE, '10401014', MAT_WGT / 1000, '10401015', MAT_WGT / 1000, '10704001', MAT_WGT / 1000, 0)) AS FG,
                SUM(DECODE(MAT_CODE, '10201001', MAT_WGT, 0)) AS GUITIE,
                SUM(DECODE(MAT_CODE, '10201006', MAT_WGT, 0)) AS GMHJ,
                SUM(DECODE(MAT_CODE, '10501024', MAT_WGT, '10705001', MAT_WGT, 0)) AS BAIHUI,
                SUM(DECODE(MAT_CODE, '10501002', MAT_WGT, '10705003', MAT_WGT, 0)) AS QSBYS,
                SUM(DECODE(MAT_CODE, '10501015', MAT_WGT, '10501029', MAT_WGT, 0)) AS BYS,
                SUM(DECODE(MAT_CODE, '10501035', MAT_WGT, '10501036', MAT_WGT, '10501012', MAT_WGT, 0)) AS SHS,
                SUM(DECODE(MAT_CODE, '10701002', MAT_WGT, 0)) AS YZ
            FROM
                SMES_B_MAT_USE
            GROUP BY
                HEAT_ID
        ) DD ON AA.HEAT_ID = DD.HEAT_ID
    WHERE
        AA.WORK_SHOP = 'L2'
      AND TRUNC(AA.STAR_CAST_TIM, 'DD') = TO_DATE('2025-08-09', 'YYYY-MM-DD')
),
-- 6. 二钢月消耗数据
monthly_consumption_l2_cte AS (
    SELECT
        NVL(SUM(CC.IRON_WGT), 0) AS IRON_WGT_M,
        NVL(SUM(DD.FG), 0) AS TOTAL_SCRAP_M,
        NVL(SUM(DD.BAIHUI), 0) AS BAIHUI_M,
        NVL(SUM(DD.QSBYS), 0) AS QSBYS_M,
        NVL(SUM(DD.BYS), 0) AS BYS_M,
        NVL(SUM(DD.SHS), 0) AS SHS_M,
        NVL(SUM(DD.GMHJ), 0) AS GMHJ_M,
        NVL(SUM(DD.GUITIE), 0) AS GUITIE_M,
        NVL(SUM(DD.YZ), 0) AS YZ_M
    FROM
        SMES_B_CCMRES AA
            LEFT JOIN
        SMES_B_BOF_RES CC ON AA.HEAT_ID = CC.HEAT_ID
            LEFT JOIN
        (
            SELECT
                HEAT_ID,
                SUM(DECODE(MAT_CODE, '10401014', MAT_WGT / 1000, '10401015', MAT_WGT / 1000, '10704001', MAT_WGT / 1000, 0)) AS FG,
                SUM(DECODE(MAT_CODE, '10201001', MAT_WGT, 0)) AS GUITIE,
                SUM(DECODE(MAT_CODE, '10201006', MAT_WGT, 0)) AS GMHJ,
                SUM(DECODE(MAT_CODE, '10501024', MAT_WGT, '10705001', MAT_WGT, 0)) AS BAIHUI,
                SUM(DECODE(MAT_CODE, '10501002', MAT_WGT, '10705003', MAT_WGT, 0)) AS QSBYS,
                SUM(DECODE(MAT_CODE, '10501015', MAT_WGT, '10501029', MAT_WGT, 0)) AS BYS,
                SUM(DECODE(MAT_CODE, '10501035', MAT_WGT, '10501036', MAT_WGT, '10501012', MAT_WGT, 0)) AS SHS,
                SUM(DECODE(MAT_CODE, '10701002', MAT_WGT, 0)) AS YZ
            FROM
                SMES_B_MAT_USE
            GROUP BY
                HEAT_ID
        ) DD ON AA.HEAT_ID = DD.HEAT_ID
    WHERE
        AA.WORK_SHOP = 'L2'
      AND AA.MAC_CODE IN ('2D312','2D313')
      AND AA.STAR_CAST_TIM >= TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
      AND AA.STAR_CAST_TIM < TO_DATE('2025-08-09', 'YYYY-MM-DD') + 1
),
-- 7. 二钢炉数（日）
daily_furnace_l2_cte AS (
    SELECT
        COUNT(*) AS FURNACE_COUNT_D
    FROM SMES_B_BOF_RES bof
    WHERE bof.WORK_SHOP = 'L2'
      AND bof.MAC_CODE IN ('2C011', '2C012')
      AND EXISTS (
        SELECT 1
        FROM SMES_B_CCMRES ccm
        WHERE ccm.heat_id = bof.HEAT_ID
          AND ccm.WORK_SHOP = 'L2'
          AND ccm.MAC_CODE IN ('2D312','2D313')
          AND TRUNC(ccm.star_cast_tim, 'DD') = TO_DATE('2025-08-09', 'YYYY-MM-DD')
    )
),
-- 8. 二钢炉数（月累计）
monthly_furnace_l2_cte AS (
    SELECT
        COUNT(*) AS FURNACE_COUNT_M
    FROM SMES_B_BOF_RES bof
    WHERE bof.WORK_SHOP = 'L2'
      AND bof.MAC_CODE IN ('2C011', '2C012')
      AND EXISTS (
        SELECT 1
        FROM SMES_B_CCMRES ccm
        WHERE ccm.heat_id = bof.HEAT_ID
          AND ccm.WORK_SHOP = 'L2'
          AND ccm.MAC_CODE IN ('2D312','2D313')
          AND ccm.star_cast_tim >= TRUNC(TO_DATE('2025-08-09', 'YYYY-MM-DD'), 'MM')
          AND ccm.star_cast_tim < TO_DATE('2025-08-09', 'YYYY-MM-DD') + 1
    )
)
-- 最终二钢数据查询
SELECT
    TO_DATE('2025-08-09', 'YYYY-MM-DD'),  -- REPORT_DATE
    'L2',                                   -- UNIT_CODE
    '炼钢二厂',                             -- UNIT_NAME

    -- 当日数据
    d_furnace.FURNACE_COUNT_D,              -- DAILY_FURNACE_COUNT
    d_prod.SLAB_WGT_D,                      -- DAILY_SLAB_OUTPUT
    (d_con.IRON_WGT_D + d_inv.PTTSPKL_D + d_inv.QMTSPKL_D), -- DAILY_IRON_CONSUMPTION
    (d_con.TOTAL_SCRAP_D + d_inv.FGPKL_D), -- DAILY_SCRAP_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.IRON_WGT_D + d_inv.PTTSPKL_D + d_inv.QMTSPKL_D) * 1000 / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_IRON_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.TOTAL_SCRAP_D + d_inv.FGPKL_D) * 1000 / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_SCRAP_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN (d_con.IRON_WGT_D + d_inv.PTTSPKL_D + d_inv.QMTSPKL_D + d_con.TOTAL_SCRAP_D + d_inv.FGPKL_D) * 1000 / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_STEEL_IRON_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BAIHUI_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_LIME_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.BYS_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_DOLOMITE_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.QSBYS_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GMHJ_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_SIMN_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.GUITIE_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_SIFE_UNIT_CONSUMPTION
    CASE WHEN d_prod.SLAB_WGT_D > 0 THEN d_con.YZ_D / d_prod.SLAB_WGT_D ELSE 0 END, -- DAILY_KILN_SLAG_UNIT_CONSUMPTION

    -- 累计数据
    m_furnace.FURNACE_COUNT_M,              -- CUMULATIVE_FURNACE_COUNT
    m_prod.SLAB_WGT_M,                      -- CUMULATIVE_SLAB_OUTPUT
    (m_con.IRON_WGT_M + m_inv.PTTSPKL_M + m_inv.QMTSPKL_M), -- CUMULATIVE_IRON_CONSUMPTION
    (m_con.TOTAL_SCRAP_M + m_inv.FGPKL_M), -- CUMULATIVE_SCRAP_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN (m_con.IRON_WGT_M + m_inv.PTTSPKL_M + m_inv.QMTSPKL_M) * 1000 / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_IRON_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN (m_con.TOTAL_SCRAP_M + m_inv.FGPKL_M) * 1000 / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_SCRAP_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN (m_con.IRON_WGT_M + m_inv.PTTSPKL_M + m_inv.QMTSPKL_M + m_con.TOTAL_SCRAP_M + m_inv.FGPKL_M) * 1000 / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_STEEL_IRON_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.BAIHUI_M / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_LIME_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.BYS_M / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.QSBYS_M / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.GMHJ_M / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_SIMN_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.GUITIE_M / m_prod.SLAB_WGT_M ELSE 0 END, -- CUMULATIVE_SIFE_UNIT_CONSUMPTION
    CASE WHEN m_prod.SLAB_WGT_M > 0 THEN m_con.YZ_M / m_prod.SLAB_WGT_M ELSE 0 END -- CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION
FROM
    daily_prod_l2_cte d_prod,
    monthly_prod_l2_cte m_prod,
    daily_inventory_l2_cte d_inv,
    monthly_inventory_l2_cte m_inv,
    daily_consumption_l2_cte d_con,
    monthly_consumption_l2_cte m_con,
    daily_furnace_l2_cte d_furnace,
    monthly_furnace_l2_cte m_furnace;

COMMIT;

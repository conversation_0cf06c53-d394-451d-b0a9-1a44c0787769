CREATE TABLE CARBON_LGMS_DAILY_REPORTER (
    -- 基础信息
    REPORT_DATE DATE NOT NULL,
    UNIT_CODE VARCHAR2(10) NOT NULL,
    UNIT_NAME VARCHAR2(50),
    
    -- 当日数据
    DAILY_FURNACE_COUNT NUMBER(20,6),
    DA<PERSON>Y_SLAB_OUTPUT NUMBER(20,6),
    DAILY_IRON_CONSUMPTION NUMBER(20,6),
    DAILY_SCRAP_CONSUMPTION NUMBER(20,6),
    DAILY_IRON_UNIT_CONSUMPTION NUMBER(20,6),
    DAILY_SCRAP_UNIT_CONSUMPTION NUMBER(20,6),
    DAILY_STEEL_IRON_CONSUMPTION NUMBER(20,6),
    DAILY_LIME_UNIT_CONSUMPTION NUMBER(20,6),
    DAILY_DOLOMITE_UNIT_CONSUMPTION NUMBER(20,6),
    D<PERSON><PERSON>Y_LIGHT_DOLOMITE_UNIT_CONSUMPTION NUMBER(20,6),
    DAILY_SIMN_UNIT_CONSUMPTION NUMBER(20,6),
    DAILY_SIFE_UNIT_CONSUMPTION NUMBER(20,6),
    DAILY_KILN_SLAG_UNIT_CONSUMPTION NUMBER(20,6),
    
    -- 累计数据
    CUMULATIVE_FURNACE_COUNT NUMBER(20,6),
    CUMULATIVE_SLAB_OUTPUT NUMBER(20,6),
    CUMULATIVE_IRON_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_SCRAP_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_IRON_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_SCRAP_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_STEEL_IRON_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_LIME_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_SIMN_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_SIFE_UNIT_CONSUMPTION NUMBER(20,6),
    CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION NUMBER(20,6)
);

-- 创建复合主键
ALTER TABLE CARBON_LGMS_DAILY_REPORTER ADD CONSTRAINT PK_CARBON_LGMS_DAILY_REPORTER PRIMARY KEY (REPORT_DATE, UNIT_CODE);

-- 创建索引
CREATE INDEX IDX_DAILY_STEEL_DATE ON CARBON_LGMS_DAILY_REPORTER(REPORT_DATE);
CREATE INDEX IDX_DAILY_STEEL_UNIT ON CARBON_LGMS_DAILY_REPORTER(UNIT_CODE);

-- 添加表注释
COMMENT ON TABLE CARBON_LGMS_DAILY_REPORTER IS '碳排放-生产日报';

-- 添加字段注释
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.REPORT_DATE IS '报表日期';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.UNIT_CODE IS '单位代码(L1-一钢,L2-二钢)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.UNIT_NAME IS '单位名称';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_FURNACE_COUNT IS '当日炉数';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_SLAB_OUTPUT IS '当日钢坯产量(吨)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_IRON_CONSUMPTION IS '当日铁水消耗量(吨)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_SCRAP_CONSUMPTION IS '当日废钢消耗量(吨)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_IRON_UNIT_CONSUMPTION IS '当日铁水单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_SCRAP_UNIT_CONSUMPTION IS '当日废钢单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_STEEL_IRON_CONSUMPTION IS '当日钢铁料消耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_LIME_UNIT_CONSUMPTION IS '当日白灰单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_DOLOMITE_UNIT_CONSUMPTION IS '当日白云石单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_LIGHT_DOLOMITE_UNIT_CONSUMPTION IS '当日轻烧白云石单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_SIMN_UNIT_CONSUMPTION IS '当日硅锰合金单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_SIFE_UNIT_CONSUMPTION IS '当日硅铁单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.DAILY_KILN_SLAG_UNIT_CONSUMPTION IS '当日窑渣单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_FURNACE_COUNT IS '累计炉数';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_SLAB_OUTPUT IS '累计钢坯产量(吨)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_IRON_CONSUMPTION IS '累计铁水消耗量(吨)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_SCRAP_CONSUMPTION IS '累计废钢消耗量(吨)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_IRON_UNIT_CONSUMPTION IS '累计铁水单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_SCRAP_UNIT_CONSUMPTION IS '累计废钢单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_STEEL_IRON_CONSUMPTION IS '累计钢铁料消耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_LIME_UNIT_CONSUMPTION IS '累计白灰单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_DOLOMITE_UNIT_CONSUMPTION IS '累计白云石单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_LIGHT_DOLOMITE_UNIT_CONSUMPTION IS '累计轻烧白云石单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_SIMN_UNIT_CONSUMPTION IS '累计硅锰合金单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_SIFE_UNIT_CONSUMPTION IS '累计硅铁单耗(kg/t)';
COMMENT ON COLUMN CARBON_LGMS_DAILY_REPORTER.CUMULATIVE_KILN_SLAG_UNIT_CONSUMPTION IS '累计窑渣单耗(kg/t)';



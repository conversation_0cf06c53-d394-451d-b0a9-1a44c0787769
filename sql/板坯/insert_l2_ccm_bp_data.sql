INSERT INTO CARBON_LGMS_L2_CCM_BP_REPORTER (HEAT_ID, TREAT_NO, WOR<PERSON>_SHOP, P_DATE,
                                            HEAT_SDL, MAT_QUL_CD, CAST_SDL, STL_WGT_WAIT,
                                            DAN<PERSON>IU, SHUANGLIU, STL_COD, STL_GRD_CD,
                                            CCM_MAC_CODE, CCM_GROUP, CCM_SHIFT, CCM_PENSHUI, CCM_CAST_SEQ_N,
                                            CCM_CAST_ID,
                                            CCM_LADLE_ID, CCM_AUTO_OPEN_FLG, CCM_TARGET_ARR_TMP, CCM_LADLE_ARR_TMP,
                                            CCM_LADLE_ARR_TIME,
                                            CCM_STAR_CAST_TIM, CCM_END_CAST_TIM, CCM_LD_DEP_TIM, CCM_YAG_TIME,
                                            CCM_TADE_TIME,
                                            CCM_LD_ARR_NET_WGHT, CCM_LD_DEP_NET_WGHT, CCM_CAST_WGT,
                                            CCM_TUNDISH_TEMP_AVG1, CCM_TUNDISH_TEMP_AVG2, CCM_TUNDISH_TEMP_AVG3,
                                            CCM_TUNDISH_TEMP_AVGR1, CCM_TUNDISH_TEMP_AVGR2, CCM_TUNDISH_TEMP_AVGR3,
                                            CCM_TD_NO, CCM_TD_NO2, CCM_MOULD_NO, CCM_MOULD_NO2,
                                            AVG_CAST_SPEED, ONESPEED, TWOSPEED,
                                            CUT_SEQ_NO, CUT_SLAB_WEIGHT,
                                            CASTING_POWDER_TYPE, COVERING_AGENT_TYPE,
                                            PART_ID_ONE, ONE_BHZ_FACTORY, PART_ID_TWO, TWO_BHZ_FACTORY,
                                            CCM_C, CCM_MN, CCM_SI, CCM_S, CCM_P, CCM_V, CCM_ALS, CCM_ALT, CCM_CA,
                                            CCM_TI, CCM_CU, CCM_NI, CCM_MO, CCM_B, CCM_N,
                                            BANPI_ZHONGBAO_FUGAIJI, BANPI_JIANXING_ZHONGBAO_FUGAIJI, BANPI_QUYANGQI,
                                            BANPI_BAOHUZHA,
                                            BANPI_DABAO_TAOGUAN, BANPI_SAIBANG, PINZHONGGANG_BAOHUZHA, BANPI_SHUIKOU,
                                            BANPI_CEWENGUAN, BANPI_GANGBAO_FUGAIJI,
                                            STRAND1_SPEED5, STRAND1_SPEED10, STRAND1_SPEED15, STRAND1_SPEED20,
                                            STRAND1_SPEED25,
                                            STRAND2_SPEED5, STRAND2_SPEED10, STRAND2_SPEED15, STRAND2_SPEED20,
                                            STRAND2_SPEED25,
                                            STRAND1_MLD_WAT_T_IN,STRAND1_MLD_WAT_T_OUT, STRAND1_MOULDWATERDELTATEMP, STRAND1_MLD_WAT_P,
                                            STRAND1_M_FT301_DIS, STRAND1_M_FT304_DIS,
                                            STRAND1_SECOND_TABLE_NAME, STRAND1_SEC_COOL_WAT_GENERAL_T,
                                            STRAND1_M_PT400_PRESSURE, STRAND1_F_ACTUAL, STRAND1_H_ACTUAL,
                                            STRAND1_MLCSSTEELLEVEL,
                                            STRAND2_MLD_WAT_T_IN,STRAND2_MLD_WAT_T_OUT, STRAND2_MOULDWATERDELTATEMP, STRAND2_MLD_WAT_P,
                                            STRAND2_M_FT301_DIS, STRAND2_M_FT304_DIS,
                                            STRAND2_SECOND_TABLE_NAME, STRAND2_SEC_COOL_WAT_GENERAL_T,
                                            STRAND2_M_PT400_PRESSURE, STRAND2_F_ACTUAL, STRAND2_H_ACTUAL,
                                            STRAND2_MLCSSTEELLEVEL,
                                            MEMO2, CCM_MEMO)
SELECT distinct H.HEAT_ID,                                                      -- HEAT_ID
                H.TREAT_NO,                                                     -- TREAT_NO
                'L2',                                                           -- WORK_SHOP
                H.STAR_CAST_TIM,                                                -- P_DATE
                H.HEAT_SDL,                                                     -- HEAT_SDL
                H.MAT_QUL_CD,                                                   -- MAT_QUL_CD
                H.CAST_SDL,                                                     -- CAST_SDL
                H.STL_WGT_WAIT,                                                 -- STL_WGT_WAIT
                F.danliu,                                                       -- DANLIU
                F.SHUANGLIU,                                                    -- SHUANGLIU
                H.STL_COD,                                                      -- STL_COD
                H.STL_GRD_CD,                                                   -- STL_GRD_CD
                H.MAC_CODE,                                                     -- CCM_MAC_CODE
                H.GROUP1,                                                       -- CCM_GROUP
                H.SHIFT,                                                        -- CCM_SHIFT
                H.PENSHUI,                                                      -- CCM_PENSHUI
                H.CAST_SEQ_N,                                                   -- CCM_CAST_SEQ_N
                H.CAST_ID,                                                      -- CCM_CAST_ID
                H.LADLE_ID,                                                     -- CCM_LADLE_ID
                H.AUTO_OPEN_FLG,                                                -- CCM_AUTO_OPEN_FLG
                H.TARGET_ARR_TMP,                                               -- CCM_TARGET_ARR_TMP
                H.LADLE_ARR_TMP,                                                -- CCM_LADLE_ARR_TMP
                H.LADLE_ARR_TIME,                                               -- CCM_LADLE_ARR_TIME
                H.STAR_CAST_TIM,                                                -- CCM_STAR_CAST_TIM
                H.END_CAST_TIM,                                                 -- CCM_END_CAST_TIM
                H.LD_DEP_TIM,                                                   -- CCM_LD_DEP_TIM
                ROUND(TO_NUMBER(H.STAR_CAST_TIM - H.LADLE_ARR_TIME) * 24 * 60), -- CCM_YAG_TIME
                ROUND(TO_NUMBER(H.END_CAST_TIM - H.STAR_CAST_TIM) * 24 * 60),   -- CCM_TADE_TIME
                H.LD_ARR_NET_WGHT,                                              -- CCM_LD_ARR_NET_WGHT
                H.LD_DEP_NET_WGHT,                                              -- CCM_LD_DEP_NET_WGHT
                H.CAST_WGT,                                                     -- CCM_CAST_WGT
                H.TUNDISH_TEMP_AVG1,                                            -- CCM_TUNDISH_TEMP_AVG1
                H.TUNDISH_TEMP_AVG2,                                            -- CCM_TUNDISH_TEMP_AVG2
                H.TUNDISH_TEMP_AVG3,                                            -- CCM_TUNDISH_TEMP_AVG3
                H.TUNDISH_TEMP_AVGR1,                                           -- CCM_TUNDISH_TEMP_AVGR1
                H.TUNDISH_TEMP_AVGR2,                                           -- CCM_TUNDISH_TEMP_AVGR2
                H.TUNDISH_TEMP_AVGR3,                                           -- CCM_TUNDISH_TEMP_AVGR3
                H.TD_NO,                                                        -- CCM_TD_NO
                H.TD_NO2,                                                       -- CCM_TD_NO2
                H.MOULD_NO,                                                     -- CCM_MOULD_NO
                H.MOULD_NO2,                                                    -- CCM_MOULD_NO2
                H.AVG_CAST_SPEED,                                               -- AVG_CAST_SPEED
                FF.ONESPEED,                                                    -- ONESPEED
                FF.TWOSPEED,                                                    -- TWOSPEED
                F.SEQ_NO,                                                       -- CUT_SEQ_NO
                F.SLAB_WEIGHT,                                                  -- CUT_SLAB_WEIGHT
                H.CASTING_POWDER_TYPE,                                          -- CASTING_POWDER_TYPE
                H.COVERING_AGENT_TYPE,                                          -- COVERING_AGENT_TYPE
                GG.PART_ID_ONE,                                                 -- PART_ID_ONE
                GG.ONE_BHZ_FACTORY,                                             -- ONE_BHZ_FACTORY
                GG.PART_ID_TWO,                                                 -- PART_ID_TWO
                GG.TWO_BHZ_FACTORY,                                             -- TWO_BHZ_FACTORY
                C.C,                                                            -- CCM_C
                C.Mn,                                                           -- CCM_MN
                C.Si,                                                           -- CCM_SI
                C.S,                                                            -- CCM_S
                C.P,                                                            -- CCM_P
                C.V,                                                            -- CCM_V
                C.ALS,                                                          -- CCM_ALS
                C.AL,                                                           -- CCM_ALT
                C.CA,                                                           -- CCM_CA
                C.TI,                                                           -- CCM_TI
                C.CU,                                                           -- CCM_CU
                C.NI,                                                           -- CCM_NI
                C.MO,                                                           -- CCM_MO
                C.B,                                                            -- CCM_B
                C.N,                                                            -- CCM_N
                N.板坯中包覆盖剂,                                               -- BANPI_ZHONGBAO_FUGAIJI
                N.板坯碱性中包覆盖剂,                                           -- BANPI_JIANXING_ZHONGBAO_FUGAIJI
                N.板坯取样器,                                                   -- BANPI_QUYANGQI
                N.板坯保护渣,                                                   -- BANPI_BAOHUZHA
                N.板坯大包套管,                                                 -- BANPI_DABAO_TAOGUAN
                N.板坯塞棒,                                                     -- BANPI_SAIBANG
                N.品种钢保护渣,                                                 -- PINZHONGGANG_BAOHUZHA
                N.板坯水口,                                                     -- BANPI_SHUIKOU
                N.板坯测温管,                                                   -- BANPI_CEWENGUAN
                N.板坯钢包覆盖剂,                                               -- BANPI_GANGBAO_FUGAIJI
                -- 流号1拉速数据(从第二个SQL关联获取)
                SPEED1.SPEED5,                                                  -- STRAND1_SPEED5
                SPEED1.SPEED10,                                                 -- STRAND1_SPEED10
                SPEED1.SPEED15,                                                 -- STRAND1_SPEED15
                SPEED1.SPEED20,                                                 -- STRAND1_SPEED20
                SPEED1.SPEED25,                                                 -- STRAND1_SPEED25
                -- 流号2拉速数据(从第三个SQL关联获取)
                SPEED2.SPEED5,                                                  -- STRAND2_SPEED5
                SPEED2.SPEED10,                                                 -- STRAND2_SPEED10
                SPEED2.SPEED15,                                                 -- STRAND2_SPEED15
                SPEED2.SPEED20,                                                 -- STRAND2_SPEED20
                SPEED2.SPEED25,                                                 -- STRAND2_SPEED25
                -- 流号1工艺参数(从第四个SQL关联获取)
                null,                                  -- STRAND1_MLD_WAT_T_IN
                null,                                 -- STRAND1_MLD_WAT_T_OUT
                null,                                -- STRAND1_MOULDWATERDELTATEMP
                PAR1.MLD_WAT_P,                                                 -- STRAND1_MLD_WAT_P
                PAR1.M_FT301_DIS,                                               -- STRAND1_M_FT301_DIS
                PAR1.M_FT304_DIS,                                               -- STRAND1_M_FT304_DIS
                PAR1.SECOND_TABLE_NAME,                                         -- STRAND1_SECOND_TABLE_NAME
                PAR1.SEC_COOL_WAT_GENERAL_T,                                    -- STRAND1_SEC_COOL_WAT_GENERAL_T
                PAR1.M_PT400_PRESSURE,                                          -- STRAND1_M_PT400_PRESSURE
                PAR1.F_ACTUAL,                                                  -- STRAND1_F_ACTUAL
                PAR1.H_ACTUAL,                                                  -- STRAND1_H_ACTUAL
                PAR1.MLCSSTEELLEVEL,                                            -- STRAND1_MLCSSTEELLEVEL
                -- 流号2工艺参数(从第五个SQL关联获取)
                null,                                  -- STRAND2_MLD_WAT_T_IN
                null,                                 -- STRAND2_MLD_WAT_T_OUT
                null,                                -- STRAND2_MOULDWATERDELTATEMP
                PAR2.MLD_WAT_P,                                                 -- STRAND2_MLD_WAT_P
                PAR2.M_FT301_DIS,                                               -- STRAND2_M_FT301_DIS
                PAR2.M_FT304_DIS,                                               -- STRAND2_M_FT304_DIS
                PAR2.SECOND_TABLE_NAME,                                         -- STRAND2_SECOND_TABLE_NAME
                PAR2.SEC_COOL_WAT_GENERAL_T,                                    -- STRAND2_SEC_COOL_WAT_GENERAL_T
                PAR2.M_PT400_PRESSURE,                                          -- STRAND2_M_PT400_PRESSURE
                PAR2.F_ACTUAL,                                                  -- STRAND2_F_ACTUAL
                PAR2.H_ACTUAL,                                                  -- STRAND2_H_ACTUAL
                PAR2.MLCSSTEELLEVEL,                                            -- STRAND2_MLCSSTEELLEVEL
                H.MEMO2,                                                        -- MEMO2
                H.MEMO                                                          -- CCM_MEMO

FROM SMES_B_CCMRES H
-- 化学成分关联
         LEFT JOIN (SELECT *
                    FROM SMES_B_che_steel a
                    WHERE a.SEQ_NO IN (SELECT max(b.SEQ_NO)
                                       FROM SMES_B_che_steel b
                                       WHERE a.HEAT_ID = b.HEAT_ID
                                       GROUP BY SUBSTR(SAMPLETYPE, 0, 1))
                      AND sampletype like 'C%') C ON H.HEAT_ID = C.HEAT_ID
-- 切割数据关联
         LEFT JOIN (SELECT SUBSTR(HEAT_ID, 0, 9)                                  HEAT_ID,
                           TREAT_NO,
                           MAX(DECODE(LIU_NO, '1', SLAB_THICKNESS || '*' || SLAB_WIDTH, '3',
                                      SLAB_THICKNESS || '*' || SLAB_WIDTH, null)) danliu,
                           MAX(DECODE(LIU_NO, '2', SLAB_THICKNESS || '*' || SLAB_WIDTH, '4',
                                      SLAB_THICKNESS || '*' || SLAB_WIDTH, null)) shuangliu,
                           sum(SLAB_WEIGHT)                                       SLAB_WEIGHT,
                           count(1)                                               SEQ_NO
                    FROM smes_b_cutres
                    GROUP BY SUBSTR(HEAT_ID, 0, 9), TREAT_NO) F ON H.HEAT_ID = F.HEAT_ID AND H.TREAT_NO = F.TREAT_NO
-- 板坯辅料消耗关联
         LEFT JOIN (SELECT HEAT_ID,
                           TREAT_NO,
                           SUM(DECODE(T.MAT_CODE, '10502010', T.MAT_WGT, 0))      板坯中包覆盖剂,
                           SUM(DECODE(T.MAT_CODE, '10504027', T.MAT_WGT, 0))      板坯碱性中包覆盖剂,
                           SUM(DECODE(T.MAT_CODE, '10504029', T.MAT_WGT, '29801040010003', T.MAT_WGT, '29801040010004',
                                      T.MAT_WGT, '29801040060001', T.MAT_WGT, 0)) 板坯取样器,
                           SUM(DECODE(T.MAT_CODE, '10504036', T.MAT_WGT, 0))      板坯保护渣,
                           SUM(DECODE(T.MAT_CODE, '10504035', T.MAT_WGT, 0))      板坯水口,
                           SUM(DECODE(T.MAT_CODE, '10504034', T.MAT_WGT, 0))      板坯大包套管,
                           SUM(DECODE(T.MAT_CODE, '10504033', T.MAT_WGT, 0))      板坯塞棒,
                           SUM(DECODE(T.MAT_CODE, '10504041', T.MAT_WGT, 0))      品种钢保护渣,
                           SUM(DECODE(T.MAT_CODE, '10504030', T.MAT_WGT, 0))      板坯测温管,
                           SUM(DECODE(T.MAT_CODE, '10504026', T.MAT_WGT, 0))      板坯钢包覆盖剂
                    FROM SMES_B_MAT_USE t
                    WHERE T.wp_code = '2D31'
                    GROUP BY HEAT_ID, TREAT_NO) N ON H.HEAT_ID = N.HEAT_ID AND H.TREAT_NO = N.TREAT_NO
-- 拉速数据关联
         LEFT JOIN (SELECT A.HEAT_ID, A.TREAT_NO, A.SPEEDVALUE ONESPEED, B.SPEEDVALUE TWOSPEED
                    FROM (SELECT * FROM SMES_B_CCMRES_STREAM WHERE STRAND_NO = '1' OR STRAND_NO = '3') A,
                         (SELECT * FROM SMES_B_CCMRES_STREAM WHERE STRAND_NO = '2' OR STRAND_NO = '4') B
                    WHERE A.HEAT_ID = B.HEAT_ID
                      AND A.TREAT_NO = B.TREAT_NO) FF ON H.HEAT_ID = FF.HEAT_ID AND H.TREAT_NO = FF.TREAT_NO
-- 部件信息关联
         LEFT JOIN (SELECT AA.HEAT_ID,
                           AA.TREAT_NO,
                           MAX(DECODE(CC.PART_NUM, '1', CC.PART_ID, ''))     PART_ID_ONE,
                           MAX(DECODE(CC.PART_NUM, '1', CC.BHZ_FACTORY, '')) ONE_BHZ_FACTORY,
                           MAX(DECODE(CC.PART_NUM, '2', CC.PART_ID, ''))     PART_ID_TWO,
                           MAX(DECODE(CC.PART_NUM, '2', CC.BHZ_FACTORY, '')) TWO_BHZ_FACTORY
                    FROM SMES_B_CCMRES AA,
                         SMES_B_CCMRES_STREAM BB,
                         SMES_C_PARTRES CC
                    WHERE AA.HEAT_ID = BB.HEAT_ID(+)
                      AND AA.TREAT_NO = BB.TREAT_NO(+)
                      AND BB.BHZ_SEQ = CC.SEQ_NO
                    GROUP BY AA.HEAT_ID, AA.TREAT_NO) GG ON H.HEAT_ID = GG.HEAT_ID AND H.TREAT_NO = GG.TREAT_NO
-- 流号1拉速数据关联(第二个SQL)
         LEFT JOIN (SELECT HEAT_ID, SPEED5, SPEED10, SPEED15, SPEED20, SPEED25
                    FROM SMES_B_CCM_SPEED
                    WHERE STRANDNO = '1') SPEED1 ON H.HEAT_ID = SPEED1.HEAT_ID
-- 流号2拉速数据关联(第三个SQL)
         LEFT JOIN (SELECT HEAT_ID, SPEED5, SPEED10, SPEED15, SPEED20, SPEED25
                    FROM SMES_B_CCM_SPEED
                    WHERE STRANDNO = '2') SPEED2 ON H.HEAT_ID = SPEED2.HEAT_ID
-- 流号1工艺参数关联(第四个SQL)
         LEFT JOIN (SELECT HEAT_ID,
                           TREAT_NO,
                           MLD_WAT_T,
                           MOULDWATERDELTATEMP,
                           MLD_WAT_P,
                           M_FT301_DIS,
                           M_FT304_DIS,
                           SECOND_TABLE_NAME,
                           SEC_COOL_WAT_GENERAL_T,
                           M_PT400_PRESSURE,
                           F_ACTUAL,
                           H_ACTUAL,
                           MLCSSTEELLEVEL
                    FROM SMES_B_CCM_PAR
                    WHERE STRAND_NO = '1') PAR1 ON H.HEAT_ID = PAR1.HEAT_ID AND H.TREAT_NO = PAR1.TREAT_NO
-- 流号2工艺参数关联(第五个SQL)
         LEFT JOIN (SELECT HEAT_ID,
                           TREAT_NO,
                           MLD_WAT_T,
                           MOULDWATERDELTATEMP,
                           MLD_WAT_P,
                           M_FT301_DIS,
                           M_FT304_DIS,
                           SECOND_TABLE_NAME,
                           SEC_COOL_WAT_GENERAL_T,
                           M_PT400_PRESSURE,
                           F_ACTUAL,
                           H_ACTUAL,
                           MLCSSTEELLEVEL
                    FROM SMES_B_CCM_PAR
                    WHERE STRAND_NO = '2') PAR2 ON H.HEAT_ID = PAR2.HEAT_ID AND H.TREAT_NO = PAR2.TREAT_NO

WHERE H.WORK_SHOP = 'L2'
  AND H.STAR_CAST_TIM BETWEEN TO_DATE('2025-08-09' || ' 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
    AND TO_DATE('2025-08-09' || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
 and H.MAC_CODE IN ('2D312','2D313')

ORDER BY H.STAR_CAST_TIM;

-- 提交事务
COMMIT;
